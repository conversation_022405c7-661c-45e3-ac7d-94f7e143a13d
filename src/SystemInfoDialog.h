#ifndef SYSTEMINFODIALOG_H
#define SYSTEMINFODIALOG_H

#include <wx/wx.h>

class SystemInfoDialog : public wxDialog
{
public:
    SystemInfoDialog(wxWindow* parent);

private:
    // Event handlers
    void OnRefresh(wxCommandEvent& event);
    void OnSave(wxCommandEvent& event);
    void OnClose(wxCommandEvent& event);
    
    // Helper methods
    wxString GetSystemInfo();
    bool SaveToFile(const wxString& content);
    
    // UI components
    wxTextCtrl* m_infoText;
    wxButton* m_refreshButton;
    wxButton* m_saveButton;
    wxButton* m_closeButton;
    
    wxDECLARE_EVENT_TABLE();
};

#endif // SYSTEMINFODIALOG_H
