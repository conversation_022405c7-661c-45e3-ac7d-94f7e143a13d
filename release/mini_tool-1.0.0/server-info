#!/bin/bash

# 定义颜色变量
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 获取当前日期和时间
current_date=$(date "+%Y年%m月%d日 %A %H:%M:%S")
# 获取主机名
hostname=$(hostname)

# 获取操作系统及版本信息
get_os_info() {
    echo -e "${YELLOW}操作系统及版本信息:${NC}"
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        echo -e "${GREEN}操作系统: $NAME${NC}"
        echo -e "${GREEN}系统版本: $PRETTY_NAME${NC}"
        echo -e "${GREEN}内核版本 : $(uname -r)"
    else
        echo -e "${RED}无法获取操作系统及版本信息${NC}"
    fi
}

# 获取主板信息
get_pm_info() {
    echo -e "${YELLOW}主板信息:${NC}"
    dmidecode -t 1 | grep -E "Manufacturer|Product Name|Serial Number"
}

# 获取CPU信息
get_cpu() {
    echo -e "${YELLOW}CPU信息:${NC}"
    
    # 强制英文输出确保解析一致性
    local lscpu_output
    lscpu_output=$(LANG=C lscpu)

    # 基础信息解析
    model=$(awk -F':[ \t]+' '/Model name/ {sub(/[[:space:]]+$/, "", $2); print $2}' <<< "$lscpu_output")
    sockets=$(awk -F':[ \t]+' '/Socket$s$/ {print $2}' <<< "$lscpu_output")
    cores_per_socket=$(awk -F':[ \t]+' '/Core$s$ per socket/ {print $2}' <<< "$lscpu_output")
    threads_per_core=$(awk -F':[ \t]+' '/Thread$s$ per core/ {print $2}' <<< "$lscpu_output")
    architecture=$(awk -F':[ \t]+' '/Architecture/ {print $2}' <<< "$lscpu_output")

    # 频率信息动态解析
    freq_base=$(awk -F':[ \t]+' '/CPU MHz/ {printf "%.2f", $2/1000}' <<< "$lscpu_output")
    freq_max=$(awk -F':[ \t]+' '/CPU max MHz/ {printf "%.2f", $2/1000}' <<< "$lscpu_output")
    freq_min=$(awk -F':[ \t]+' '/CPU min MHz/ {printf "%.2f", $2/1000}' <<< "$lscpu_output")

    # 输出动态生成信息
    echo -e "${GREEN}型号          : ${model}${NC}"
    echo -e "${GREEN}架构          : ${architecture}${NC}"
    echo -e "${GREEN}物理CPU数     : ${sockets:-1}${NC}"
    

    # 动态频率显示
    echo -e "${GREEN}基础频率      : ${freq_base} GHz${NC}"
    [[ -n "$freq_max" ]] && echo -e "${GREEN}最大加速频率  : ${freq_max} GHz${NC}"
    [[ -n "$freq_min" ]] && echo -e "${GREEN}最低运行频率  : ${freq_min} GHz${NC}"
    echo
}

# 新增内存信息获取函数（从/proc/meminfo获取）
get_mem_simple() {
    echo -e "${YELLOW}内存:${NC}"
    if [ -f /proc/meminfo ]; then
        # 提取内存信息
        mem_total=$(grep MemTotal /proc/meminfo | awk '{printf "%.2f GiB", $2 / 1024 / 1024}')
        mem_free=$(grep MemFree /proc/meminfo | awk '{printf "%.2f GiB", $2 / 1024 / 1024}')
        buffers=$(grep Buffers /proc/meminfo | awk '{printf "%.2f GiB", $2 / 1024 / 1024}')
        cached=$(grep -i '^Cached' /proc/meminfo | awk '{printf "%.2f GiB", $2 / 1024 / 1024}')
        available=$(grep MemAvailable /proc/meminfo | awk '{printf "%.2f GiB", $2 / 1024 / 1024}')

        # 输出内存摘要信息
        echo -e "${GREEN}总内存: $mem_total${NC}"
        echo -e "${GREEN}空闲内存: $mem_free${NC}"
        echo -e "${GREEN}Buffers: $buffers${NC}"
        echo -e "${GREEN}Cached: $cached${NC}"
        echo -e "${GREEN}可用内存: $available${NC}"
    else
        echo -e "${RED}无法访问 /proc/meminfo，系统可能不支持此功能。${NC}"
    fi
}

# 获取内存信息
get_mem() {
    echo -e "${YELLOW}内存信息:${NC}"
    dmidecode_output=$(dmidecode -t memory)

    # 计算总内存，正确处理MB和GB单位
    total_memory=$(echo "$dmidecode_output" | grep -A 10 "Memory Device" | grep "Size:" | grep -v "No Module Installed" | awk '{
        if ($3 == "MB") total += $2 / 1024
        else if ($3 == "GB") total += $2
    } END {printf "%.2f GiB", total}')
    echo -e "${GREEN}当前总内存: $total_memory${NC}"

    # 提取内存槽位总数
    total_slots=$(echo "$dmidecode_output" | grep "Number Of Devices" | awk '{print $NF}')
    echo -e "${GREEN}内存槽位总数: $total_slots${NC}"

    # 提取已安装的内存模块数量
    installed_memory=$(echo "$dmidecode_output" | grep -v Volatile | grep -c "Size: [0-9]")
    echo -e "${GREEN}已安装的内存模块数量: $installed_memory${NC}"

    # 计算未使用的槽位数量
    unused_slots=$((total_slots - installed_memory))
    if [ "$unused_slots" -lt 0 ]; then
        unused_slots=0
    fi
    echo -e "${GREEN}未使用的槽位数量: $unused_slots${NC}"

    # 提取每根内存的详细信息
    echo -e "${GREEN}已安装内存的详细信息：${NC}"
    echo "$dmidecode_output" | grep -A 32 "Memory Device" | head -22 | grep -E -w "Manufacturer|Type:|Size|Speed|Part Number" | sed '/No Module Installed/d'
}

# 获取磁盘信息
get_disk() {
    echo -e "${YELLOW}磁盘信息:${NC}"
    lsblk -d -o NAME,TYPE,SIZE | grep -v loop
}

# 获取物理网卡信息
get_nic_info() {
    echo -e "${YELLOW}物理网卡信息:${NC}"
    # 获取物理网卡信息，过滤掉虚拟网卡（如docker网卡）
    nic_info=$(ip -o link show | awk -F': ' '{print $2}' | grep -E '^(eth|ens|enp)')
    while IFS= read -r line; do
        echo -e "${GREEN}网卡名称: $line${NC}"
        ip_addr=$(ip -o -4 addr show $line | awk '{print $4}')
        if ip -o link show $line | grep -q "state UP"; then
            echo -e "${GREEN}状态: 已连接, IP地址：$ip_addr${NC}"
        else
            echo -e "${RED}状态: 未连接${NC}"
        fi
    done <<< "$nic_info"
}

# 定义获取显卡信息的函数
get_gpu_info() {
    echo -e "${YELLOW}GPU信息:${NC}"
    if command -v nvidia-smi &> /dev/null; then
        # 获取显卡型号
        gpu_model=$(nvidia-smi -L | head -n 1 | grep -oP 'GPU 0: \K.*(?= \()')
        
        # 获取显卡数量
        gpu_count=$(nvidia-smi -L | wc -l)
        
        # 获取显存信息（单位：MB）
        memory_total_mb=$(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits | head -n 1)
        
        # 获取默认功率（单位：W）
        power_limit_w=$(nvidia-smi --query-gpu=power.limit --format=csv,noheader,nounits | head -n 1)
        
        # 将显存从 MB 转换为 GB（使用十进制方式）
        memory_total_gb=$(echo "scale=2; $memory_total_mb / 1000" | bc)
        
        # 输出汇总信息
        echo -e "${GREEN}显卡型号: $gpu_model${NC}"
        echo -e "${GREEN}显卡数量: $gpu_count${NC}"
        echo -e "${GREEN}显卡内存: $memory_total_gb GB${NC}"
        echo -e "${GREEN}最大功率: $power_limit_w W${NC}"
    else
        echo -e "${RED}无GPU信息${NC}"
    fi
}

# 主函数
main() {
    echo -e "${YELLOW}==================== 服务器硬件信息报告 ====================${NC}"
    echo -e "${YELLOW}日期: $current_date${NC}"
    echo -e "${YELLOW}主机名: $hostname${NC}"
    echo -e "${YELLOW}============================================================${NC}"
    get_os_info
    get_pm_info
    get_cpu
    get_mem_simple
    get_disk
    get_nic_info
    get_gpu_info
}

# 执行主函数并将输出保存到文件
main | tee /dev/tty | sed 's/\x1b\[[0-9;]*m//g' > /tmp/server-info.txt

