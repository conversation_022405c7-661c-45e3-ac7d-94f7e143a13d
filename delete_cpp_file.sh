#!/bin/bash


DIRECTORY="/home/<USER>/Nuctech_Services/OSS_MR/src/mgsPkg"

find "$DIRECTORY" -type f -name "*.cpp" -exec rm -v {} \;

echo "mgspkg cpp file deleted success"

sleep 1

DIRECTORY="/home/<USER>/Nuctech_Services/OSS_MR/src/http_module"

find "$DIRECTORY" -type f -name "*.cpp" -exec rm -v {} \;

echo "http_module cpp file deleted success"
sleep 1

DIRECTORY="/home/<USER>/Nuctech_Services/OSS_MR/src/mcb_parser"

find "$DIRECTORY" -type f -name "*.cpp" -exec rm -v {} \;

echo "mcb_parser cpp file deleted success"
sleep 1

DIRECTORY="/home/<USER>/Nuctech_Services/OSS_MR/src/mcb_reader"

find "$DIRECTORY" -type f -name "*.cpp" -exec rm -v {} \;

echo "mcb_reader cpp file deleted success"
sleep 1

DIRECTORY="/home/<USER>/Nuctech_Services/OSS_MR/src/mcb_recorder"

find "$DIRECTORY" -type f -name "*.cpp" -exec rm -v {} \;

echo "mcb_recorder cpp file deleted success"
sleep 1

DIRECTORY="/home/<USER>/Nuctech_Services/OSS_MR/src/process_collection"

find "$DIRECTORY" -type f -name "*.cpp" -exec rm -v {} \;

echo "process_collection cpp file deleted success"

echo "delete all cpp file success!!!!"

