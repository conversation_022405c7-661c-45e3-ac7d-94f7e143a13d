#include "MCPClient.h"
#include <wx/msgdlg.h>
#include <wx/utils.h>
#include <wx/log.h>
#include <unistd.h>
#include <sys/wait.h>

// 常量定义
const wxString MCPClient::MCP_VERSION = wxT("2025-06-18");
const int MCPClient::TIMEOUT_MS = 5000;

MCPClient::MCPClient()
    : m_serverProcess(nullptr),
      m_serverPid(0),
      m_inputStream(nullptr),
      m_outputStream(nullptr),
      m_initialized(false),
      m_serverRunning(false),
      m_nextRequestId(1)
{
}

MCPClient::~MCPClient()
{
    StopServer();
}

bool MCPClient::StartServer(const wxString& command, const wxArrayString& args)
{
    if (m_serverRunning) {
        wxLogMessage(wxT("MCP服务器已在运行"));
        return true;
    }

    // 构建完整命令
    wxString fullCommand = command;
    for (size_t i = 0; i < args.GetCount(); i++) {
        fullCommand += wxT(" \"") + args[i] + wxT("\"");
    }

    // wxLogMessage(wxT("执行命令: %s"), fullCommand);

    // 创建进程对象
    m_serverProcess = new wxProcess(nullptr, wxID_ANY);
    m_serverProcess->Redirect();

    // 启动进程
    m_serverPid = wxExecute(fullCommand, wxEXEC_ASYNC, m_serverProcess);

    // wxLogMessage(wxT("进程ID: %ld"), m_serverPid);

    if (m_serverPid == 0) {
        wxLogError(wxT("无法启动MCP服务器进程"));
        delete m_serverProcess;
        m_serverProcess = nullptr;
        return false;
    }

    // 获取输入输出流
    m_inputStream = m_serverProcess->GetInputStream();
    m_outputStream = m_serverProcess->GetOutputStream();
    
    if (!m_inputStream || !m_outputStream) {
        StopServer();
        return false;
    }

    m_serverRunning = true;
    
    // 等待一小段时间让服务器启动
    wxMilliSleep(500);
    
    return true;
}

void MCPClient::StopServer()
{
    if (m_serverProcess) {
        if (m_serverPid > 0) {
            // 尝试优雅关闭
            kill(m_serverPid, SIGTERM);
            
            // 等待进程结束
            int status;
            if (waitpid(m_serverPid, &status, WNOHANG) == 0) {
                // 如果进程还在运行，强制杀死
                wxMilliSleep(1000);
                kill(m_serverPid, SIGKILL);
                waitpid(m_serverPid, &status, 0);
            }
        }
        
        delete m_serverProcess;
        m_serverProcess = nullptr;
    }
    
    m_serverPid = 0;
    m_inputStream = nullptr;
    m_outputStream = nullptr;
    m_serverRunning = false;
    m_initialized = false;
    m_availableTools.clear();
}

bool MCPClient::IsServerRunning() const
{
    return m_serverRunning && m_serverProcess != nullptr;
}

bool MCPClient::Initialize()
{
    if (!IsServerRunning()) {
        wxLogError(wxT("服务器未运行，无法初始化"));
        return false;
    }

    if (m_initialized) {
        wxLogMessage(wxT("MCP客户端已初始化"));
        return true;
    }

    // wxLogMessage(wxT("开始MCP初始化流程..."));

    // 发送初始化请求
    // wxLogMessage(wxT("发送初始化请求..."));
    if (!SendInitialize()) {
        wxLogError(wxT("发送初始化请求失败"));
        return false;
    }
    // wxLogMessage(wxT("初始化请求发送成功"));

    // 发送初始化完成通知
    // wxLogMessage(wxT("发送初始化完成通知..."));
    if (!SendInitialized()) {
        wxLogError(wxT("发送初始化完成通知失败"));
        return false;
    }
    // wxLogMessage(wxT("初始化完成通知发送成功"));

    // 获取工具列表
    // wxLogMessage(wxT("获取工具列表..."));
    if (!ListTools()) {
        wxLogError(wxT("获取工具列表失败"));
        return false;
    }
    // wxLogMessage(wxT("工具列表获取成功，共 %d 个工具"), (int)m_availableTools.size());

    m_initialized = true;
    // wxLogMessage(wxT("MCP初始化完成"));
    return true;
}

std::vector<MCPTool> MCPClient::GetAvailableTools()
{
    if (!m_initialized) {
        Initialize();
    }
    
    return m_availableTools;
}

MCPToolResult MCPClient::CallTool(const MCPToolCall& toolCall)
{
    if (!m_initialized) {
        return MCPToolResult(toolCall.callId, true, wxT("MCP client not initialized"));
    }

    // 验证工具名称
    bool toolExists = false;
    for (const auto& tool : m_availableTools) {
        if (tool.name == toolCall.name) {
            toolExists = true;
            break;
        }
    }

    if (!toolExists) {
        return MCPToolResult(toolCall.callId, true,
                           wxString::Format(wxT("Unknown tool: %s"), toolCall.name));
    }

    try {
        // 创建工具调用请求
        json params = json::object();
        params["name"] = toolCall.name.ToStdString();
        params["arguments"] = toolCall.arguments;

        json request = CreateRequest(wxT("tools/call"), params);
        json response;

        if (!SendRequest(request, response)) {
            return MCPToolResult(toolCall.callId, true, wxT("Failed to send tool call request"));
        }

        // 解析响应
        if (response.contains("error")) {
            wxString errorMsg = wxString::FromUTF8(response["error"]["message"].get<std::string>().c_str());
            return MCPToolResult(toolCall.callId, true, errorMsg);
        }

        if (response.contains("result")) {
            json result = response["result"];
            bool isError = result.value("isError", false);

            wxString content;
            if (result.contains("content") && result["content"].is_array()) {
                for (const auto& item : result["content"]) {
                    if (item.contains("text")) {
                        content += wxString::FromUTF8(item["text"].get<std::string>().c_str());
                    }
                }
            }

            return MCPToolResult(toolCall.callId, isError, content);
        }

        return MCPToolResult(toolCall.callId, true, wxT("Invalid response format"));

    } catch (const std::exception& e) {
        return MCPToolResult(toolCall.callId, true,
                           wxString::Format(wxT("Tool execution error: %s"),
                                          wxString::FromUTF8(e.what())));
    }
}

wxString MCPClient::GenerateToolsDescription() const
{
    if (m_availableTools.empty()) {
        return wxEmptyString;
    }
    
    wxString description = wxT("你可以使用以下工具来操作本地文件系统：\n\n");
    
    for (const auto& tool : m_availableTools) {
        description += wxString::Format(wxT("工具名称: %s\n"), tool.name);
        description += wxString::Format(wxT("描述: %s\n"), tool.description);
        
        // 添加参数信息
        if (tool.inputSchema.contains("properties")) {
            description += wxT("参数:\n");
            for (const auto& [key, value] : tool.inputSchema["properties"].items()) {
                wxString paramName = wxString::FromUTF8(key.c_str());
                wxString paramType = wxT("string");
                if (value.contains("type")) {
                    paramType = wxString::FromUTF8(value["type"].get<std::string>().c_str());
                }
                description += wxString::Format(wxT("  - %s (%s)"), paramName, paramType);
                
                if (value.contains("description")) {
                    wxString paramDesc = wxString::FromUTF8(value["description"].get<std::string>().c_str());
                    description += wxString::Format(wxT(": %s"), paramDesc);
                }
                description += wxT("\n");
            }
        }
        description += wxT("\n");
    }
    
    description += wxT("要使用工具，请在你的回复中包含JSON格式的工具调用，例如：\n");
    description += wxT("```json\n");
    description += wxT("{\n");
    description += wxT("  \"tool_calls\": [\n");
    description += wxT("    {\n");
    description += wxT("      \"name\": \"工具名称\",\n");
    description += wxT("      \"arguments\": {\n");
    description += wxT("        \"参数名\": \"参数值\"\n");
    description += wxT("      }\n");
    description += wxT("    }\n");
    description += wxT("  ]\n");
    description += wxT("}\n");
    description += wxT("```\n");
    
    return description;
}

// 私有方法实现

bool MCPClient::SendRequest(const json& request, json& response)
{
    if (!IsServerRunning()) {
        return false;
    }

    // 将请求转换为字符串并发送
    wxString requestStr = wxString::FromUTF8(request.dump().c_str()) + wxT("\n");

    if (!WriteToServer(requestStr)) {
        return false;
    }

    // 读取响应
    wxString responseStr = ReadFromServer();
    if (responseStr.IsEmpty()) {
        return false;
    }

    // 处理多个JSON消息的情况
    wxString targetResponse;
    int requestId = request.value("id", -1);

    // 按行分割响应
    wxArrayString lines = wxSplit(responseStr, '\n');
    for (size_t i = 0; i < lines.GetCount(); i++) {
        wxString line = lines[i].Trim().Trim(false);
        if (line.IsEmpty()) continue;

        try {
            json jsonLine = json::parse(line.ToStdString());

            // 如果这是我们要找的响应（匹配ID）
            if (jsonLine.contains("id") && jsonLine["id"] == requestId) {
                targetResponse = line;
                break;
            }
            // 如果是错误响应且ID匹配
            else if (jsonLine.contains("error") && jsonLine.contains("id") && jsonLine["id"] == requestId) {
                targetResponse = line;
                break;
            }
        } catch (const json::parse_error& e) {
            wxLogDebug(wxT("跳过无效JSON行: %s, 错误: %s"), line, wxString::FromUTF8(e.what()));
            continue;
        }
    }

    if (targetResponse.IsEmpty()) {
        wxLogError(wxT("未找到匹配的响应，请求ID: %d"), requestId);
        return false;
    }

    try {
        response = json::parse(targetResponse.ToStdString());
        return true;
    } catch (const json::parse_error& e) {
        wxLogError(wxT("Failed to parse MCP response: %s"), wxString::FromUTF8(e.what()));
        return false;
    }
}

bool MCPClient::SendNotification(const json& notification)
{
    if (!IsServerRunning()) {
        return false;
    }

    wxString notificationStr = wxString::FromUTF8(notification.dump().c_str()) + wxT("\n");
    return WriteToServer(notificationStr);
}

bool MCPClient::SendInitialize()
{
    json params = json::object();
    params["protocolVersion"] = MCP_VERSION.ToStdString();
    params["capabilities"] = json::object();
    params["clientInfo"] = json::object();
    params["clientInfo"]["name"] = "MiniTool";
    params["clientInfo"]["version"] = "1.0.0";

    json request = CreateRequest(wxT("initialize"), params);

    // wxLogMessage(wxT("发送初始化请求: %s"), wxString::FromUTF8(request.dump().c_str()));

    json response;

    if (!SendRequest(request, response)) {
        wxLogError(wxT("发送初始化请求失败"));
        return false;
    }

    // wxLogMessage(wxT("收到初始化响应: %s"), wxString::FromUTF8(response.dump().c_str()));

    // 检查响应
    if (response.contains("error")) {
        wxLogError(wxT("MCP initialize failed: %s"),
                   wxString::FromUTF8(response["error"]["message"].get<std::string>().c_str()));
        return false;
    }

    if (!response.contains("result")) {
        wxLogError(wxT("初始化响应缺少result字段"));
        return false;
    }

    // wxLogMessage(wxT("初始化请求成功"));
    return true;
}

bool MCPClient::SendInitialized()
{
    json notification = CreateNotification(wxT("notifications/initialized"));
    return SendNotification(notification);
}

bool MCPClient::ListTools()
{
    json request = CreateRequest(wxT("tools/list"));
    json response;

    if (!SendRequest(request, response)) {
        return false;
    }

    if (response.contains("error")) {
        wxLogError(wxT("Failed to list tools: %s"),
                   wxString::FromUTF8(response["error"]["message"].get<std::string>().c_str()));
        return false;
    }

    if (!response.contains("result") || !response["result"].contains("tools")) {
        return false;
    }

    // 解析工具列表
    m_availableTools.clear();
    for (const auto& tool : response["result"]["tools"]) {
        if (tool.contains("name") && tool.contains("description")) {
            wxString name = wxString::FromUTF8(tool["name"].get<std::string>().c_str());
            wxString description = wxString::FromUTF8(tool["description"].get<std::string>().c_str());
            json inputSchema = tool.value("inputSchema", json::object());

            m_availableTools.emplace_back(name, description, inputSchema);
        }
    }

    return true;
}

wxString MCPClient::ReadFromServer()
{
    if (!m_inputStream) {
        wxLogError(wxT("输入流为空"));
        return wxEmptyString;
    }

    if (!m_inputStream->CanRead()) {
        // 等待数据可读
    }

    wxString result;
    char buffer[4096];

    // 设置超时
    int attempts = 0;
    const int maxAttempts = TIMEOUT_MS / 100;

    // wxLogMessage(wxT("开始从服务器读取数据，最大等待 %d 毫秒"), TIMEOUT_MS);

    while (attempts < maxAttempts) {
        if (m_inputStream->CanRead()) {
            size_t bytesRead = m_inputStream->Read(buffer, sizeof(buffer) - 1).LastRead();
            if (bytesRead > 0) {
                buffer[bytesRead] = '\0';
                result += wxString::FromUTF8(buffer);

                // wxLogMessage(wxT("读取到 %d 字节数据: %s"), (int)bytesRead, wxString::FromUTF8(buffer));

                // 检查是否读取到完整的JSON消息（以换行符结束）
                if (result.EndsWith(wxT("\n"))) {
                    result.Trim();
                    // wxLogMessage(wxT("读取完整消息: %s"), result);
                    break;
                }
            }
        }

        wxMilliSleep(100);
        attempts++;

        if (attempts % 10 == 0) {
            // 等待数据中...
        }
    }

    if (result.IsEmpty()) {
        wxLogError(wxT("读取超时，未收到任何数据"));
    }

    return result;
}

bool MCPClient::WriteToServer(const wxString& data)
{
    if (!m_outputStream) {
        return false;
    }

    wxCharBuffer utf8Buffer = data.ToUTF8();
    if (!utf8Buffer.data()) {
        return false;
    }

    size_t dataLen = strlen(utf8Buffer.data());
    m_outputStream->Write(utf8Buffer.data(), dataLen);

    return m_outputStream->GetLastError() == wxSTREAM_NO_ERROR;
}

json MCPClient::CreateRequest(const wxString& method, const json& params)
{
    json request = json::object();
    request["jsonrpc"] = "2.0";
    request["id"] = m_nextRequestId++;
    request["method"] = method.ToStdString();

    if (!params.is_null() && !params.empty()) {
        request["params"] = params;
    }

    return request;
}

json MCPClient::CreateNotification(const wxString& method, const json& params)
{
    json notification = json::object();
    notification["jsonrpc"] = "2.0";
    notification["method"] = method.ToStdString();

    if (!params.is_null() && !params.empty()) {
        notification["params"] = params;
    }

    return notification;
}
