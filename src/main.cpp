#include <wx/wx.h>
#include "MainFrame.h"

class MiniToolApp : public wxApp
{
public:
    virtual bool OnInit();
};

wxIMPLEMENT_APP(MiniToolApp);

bool MiniToolApp::OnInit()
{
    MainFrame* frame = new MainFrame(wxT("Mini Tool - 小工具集合"), wxPoint(50, 50), wxSize(800, 600));
    frame->Show(true);

    // Show login dialog immediately after showing the main frame
    if (!frame->ShowLoginDialog()) {
        // User cancelled login, close the application
        frame->Close(true);
        return false;
    }

    return true;
}
