#include "AiChatDialog.h"
#include <wx/msgdlg.h>
#include <wx/sizer.h>
#include <wx/stattext.h>
#include <wx/button.h>
#include <wx/textctrl.h>
#include <wx/panel.h>
#include <wx/font.h>
#include <wx/colour.h>
#include <wx/utils.h>
#include <wx/stream.h>
#include <wx/url.h>
#include <wx/protocol/http.h>
#include <fstream>
#include <cstdio>
#include <unistd.h>
#include <cstring>
#include <sys/wait.h>
#include <signal.h>
#include <wx/filename.h>
#include <wx/stdpaths.h>

using json = nlohmann::json;

// 常量定义
const wxString AiChatDialog::OLLAMA_COMMAND = wxT("ollama run qwen3:8b");
const wxString AiChatDialog::OLLAMA_API_URL = wxT("http://localhost:11434/api/chat");
const wxString AiChatDialog::QWEN_AGENT_API_URL = wxT("http://127.0.0.1:5000/chat");
const wxString AiChatDialog::QWEN_AGENT_HEALTH_URL = wxT("http://127.0.0.1:5000/health");
const int AiChatDialog::STATUS_CHECK_INTERVAL = 2000; // 2秒

wxBEGIN_EVENT_TABLE(AiChatDialog, wxDialog)
    EVT_BUTTON(ID_CONFIRM_AI, AiChatDialog::OnConfirm)
    EVT_BUTTON(ID_CANCEL_AI, AiChatDialog::OnCancel)
    EVT_BUTTON(ID_SEND_MESSAGE, AiChatDialog::OnSendMessage)
    EVT_TEXT_ENTER(ID_INPUT_TEXT, AiChatDialog::OnInputEnter)
    EVT_CLOSE(AiChatDialog::OnClose)
    EVT_TIMER(ID_STATUS_TIMER, AiChatDialog::OnTimer)
wxEND_EVENT_TABLE()

AiChatDialog::AiChatDialog(wxWindow* parent)
    : wxDialog(parent, wxID_ANY, wxT("智能聊天"), wxDefaultPosition, wxSize(800, 600),
               wxDEFAULT_DIALOG_STYLE | wxRESIZE_BORDER),
      m_confirmPanel(nullptr),
      m_chatPanel(nullptr),
      m_currentState(STATE_CONFIRMATION),
      m_statusTimer(nullptr),
      m_ollamaProcess(nullptr),
      m_ollamaPid(0),
      m_waitingForResponse(false),
      m_mcpClient(std::make_unique<MCPClient>()),
      m_assistantTextAttr(*wxBLUE),
      m_streamFilePosition(0),
      m_qwenAgentProcess(nullptr),
      m_qwenAgentPid(0),
      m_sessionId(wxEmptyString)
{
    // 创建定时器
    m_statusTimer = new wxTimer(this, ID_STATUS_TIMER);

    // 创建主布局
    wxBoxSizer* mainSizer = new wxBoxSizer(wxVERTICAL);
    SetSizer(mainSizer);

    // 创建确认页面
    CreateConfirmationPage();

    // 居中显示
    Centre();
}

AiChatDialog::~AiChatDialog()
{
    if (m_statusTimer && m_statusTimer->IsRunning()) {
        m_statusTimer->Stop();
    }

    // 停止Ollama进程
    StopOllama();

    // 停止Qwen-Agent服务
    StopQwenAgentService();

    // 停止MCP服务器
    if (m_mcpClient) {
        m_mcpClient->StopServer();
    }
}

void AiChatDialog::CreateConfirmationPage()
{
    m_confirmPanel = new wxPanel(this, wxID_ANY);
    m_confirmPanel->SetBackgroundColour(wxColour(250, 250, 250));
    
    // 创建主布局
    wxBoxSizer* mainSizer = new wxBoxSizer(wxVERTICAL);
    
    // 警告图标和标题
    wxBoxSizer* titleSizer = new wxBoxSizer(wxHORIZONTAL);
    
    // 警告标题
    m_warningTitle = new wxStaticText(m_confirmPanel, wxID_ANY, 
        wxT("⚠️ 智能聊天功能警告"));
    wxFont titleFont = m_warningTitle->GetFont();
    titleFont.SetPointSize(16);
    titleFont.SetWeight(wxFONTWEIGHT_BOLD);
    m_warningTitle->SetFont(titleFont);
    m_warningTitle->SetForegroundColour(wxColour(200, 100, 0));
    
    titleSizer->Add(m_warningTitle, 1, wxALL | wxALIGN_CENTER_VERTICAL, 10);
    mainSizer->Add(titleSizer, 0, wxEXPAND);
    
    // 警告内容
    m_warningText = new wxStaticText(m_confirmPanel, wxID_ANY,
        wxT("智能聊天功能会占用大量GPU资源，可能会影响设备的其他功能。\n\n")
        wxT("启动此功能将：\n")
        wxT("• 运行 Qwen-Agent 服务和 Qwen3:8b 模型\n")
        wxT("• 占用大量显存和计算资源\n")
        wxT("• 可能影响系统其他应用的性能\n")
        wxT("• 增加设备功耗和发热\n\n")
        wxT("请确保您的设备有足够的资源，并且当前没有运行其他重要的GPU密集型任务。\n\n")
        wxT("是否继续启动智能聊天功能？"));
    
    wxFont textFont = m_warningText->GetFont();
    textFont.SetPointSize(11);
    m_warningText->SetFont(textFont);
    m_warningText->Wrap(700);
    
    mainSizer->Add(m_warningText, 1, wxALL | wxEXPAND, 20);
    
    // 按钮
    wxBoxSizer* buttonSizer = new wxBoxSizer(wxHORIZONTAL);
    
    m_cancelButton = new wxButton(m_confirmPanel, ID_CANCEL_AI, wxT("取消"));
    m_confirmButton = new wxButton(m_confirmPanel, ID_CONFIRM_AI, wxT("确认启动"));
    
    // 设置确认按钮为默认按钮
    m_confirmButton->SetDefault();
    m_confirmButton->SetBackgroundColour(wxColour(220, 100, 100));
    m_confirmButton->SetForegroundColour(wxColour(255, 255, 255));
    
    buttonSizer->Add(m_cancelButton, 0, wxALL, 5);
    buttonSizer->AddStretchSpacer();
    buttonSizer->Add(m_confirmButton, 0, wxALL, 5);
    
    mainSizer->Add(buttonSizer, 0, wxALL | wxEXPAND, 20);
    
    m_confirmPanel->SetSizer(mainSizer);

    // 添加到主对话框的sizer中
    wxSizer* dialogSizer = GetSizer();
    if (dialogSizer) {
        dialogSizer->Add(m_confirmPanel, 1, wxEXPAND);
    }
}

void AiChatDialog::CreateChatPage()
{
    if (m_chatPanel) {
        return; // 已经创建过了
    }
    
    m_chatPanel = new wxPanel(this, wxID_ANY);
    m_chatPanel->SetBackgroundColour(wxColour(245, 245, 245));
    
    // 创建主布局
    wxBoxSizer* mainSizer = new wxBoxSizer(wxVERTICAL);
    
    // 标题
    wxStaticText* titleText = new wxStaticText(m_chatPanel, wxID_ANY, 
        wxT("智能聊天"));
    wxFont titleFont = titleText->GetFont();
    titleFont.SetPointSize(14);
    titleFont.SetWeight(wxFONTWEIGHT_BOLD);
    titleText->SetFont(titleFont);
    mainSizer->Add(titleText, 0, wxALL | wxCENTER, 10);
    
    // 聊天显示区域
    m_chatDisplay = new wxTextCtrl(m_chatPanel, wxID_ANY, wxEmptyString,
        wxDefaultPosition, wxDefaultSize,
        wxTE_MULTILINE | wxTE_READONLY | wxTE_WORDWRAP);
    
    // 设置聊天显示区域的字体
    wxFont chatFont(10, wxFONTFAMILY_DEFAULT, wxFONTSTYLE_NORMAL, wxFONTWEIGHT_NORMAL);
    m_chatDisplay->SetFont(chatFont);
    m_chatDisplay->SetBackgroundColour(wxColour(255, 255, 255));
    
    mainSizer->Add(m_chatDisplay, 1, wxALL | wxEXPAND, 10);
    
    // 输入区域
    wxBoxSizer* inputSizer = new wxBoxSizer(wxHORIZONTAL);
    
    m_inputText = new wxTextCtrl(m_chatPanel, ID_INPUT_TEXT, wxEmptyString,
        wxDefaultPosition, wxDefaultSize, wxTE_PROCESS_ENTER);
    m_inputText->SetHint(wxT("请输入您的问题..."));
    
    m_sendButton = new wxButton(m_chatPanel, ID_SEND_MESSAGE, wxT("发送"));
    m_sendButton->SetBackgroundColour(wxColour(100, 150, 255));
    m_sendButton->SetForegroundColour(wxColour(255, 255, 255));
    
    inputSizer->Add(m_inputText, 1, wxALL | wxALIGN_CENTER_VERTICAL, 5);
    inputSizer->Add(m_sendButton, 0, wxALL | wxALIGN_CENTER_VERTICAL, 5);
    
    mainSizer->Add(inputSizer, 0, wxALL | wxEXPAND, 10);
    
    // 状态文本
    m_statusText = new wxStaticText(m_chatPanel, wxID_ANY, wxT("就绪"));
    m_statusText->SetForegroundColour(wxColour(100, 100, 100));
    mainSizer->Add(m_statusText, 0, wxALL | wxCENTER, 5);
    
    m_chatPanel->SetSizer(mainSizer);

    // 添加到主对话框的sizer中（但先隐藏）
    wxSizer* dialogSizer = GetSizer();
    if (dialogSizer) {
        dialogSizer->Add(m_chatPanel, 1, wxEXPAND);
    }
    m_chatPanel->Show(false);

    // 初始化聊天显示
    m_chatDisplay->SetValue(wxT("欢迎使用智能聊天功能！\n")
                           wxT("基于 Qwen-Agent 的智能助手为您服务。\n\n"));
}

void AiChatDialog::SwitchToState(DialogState newState)
{
    m_currentState = newState;

    // 隐藏所有面板
    if (m_confirmPanel) {
        m_confirmPanel->Show(false);
    }
    if (m_chatPanel) {
        m_chatPanel->Show(false);
    }

    // 根据状态显示对应的面板
    switch (newState) {
        case STATE_CONFIRMATION:
            if (m_confirmPanel) {
                m_confirmPanel->Show(true);
            }
            SetTitle(wxT("智能聊天 - 确认"));
            break;

        case STATE_STARTING:
            if (!m_chatPanel) {
                CreateChatPage();
            }
            m_chatPanel->Show(true);

            if (m_statusText) {
                m_statusText->SetLabel(wxT("正在启动 Qwen-Agent 服务，请稍候..."));
            }
            if (m_inputText) {
                m_inputText->Enable(false);
            }
            if (m_sendButton) {
                m_sendButton->Enable(false);
            }
            SetTitle(wxT("智能聊天 - 启动中"));
            break;

        case STATE_CHATTING:
            if (m_chatPanel) {
                m_chatPanel->Show(true);
            }

            if (m_statusText) {
                m_statusText->SetLabel(wxT("就绪"));
            }
            if (m_inputText) {
                m_inputText->Enable(true);
                m_inputText->SetFocus();
            }
            if (m_sendButton) {
                m_sendButton->Enable(true);
            }
            SetTitle(wxT("智能聊天"));
            break;
    }

    Layout();
    Refresh();
}

void AiChatDialog::OnConfirm(wxCommandEvent& WXUNUSED(event))
{
    // 切换到启动状态
    SwitchToState(STATE_STARTING);

    // 启动MCP服务器
    wxArrayString mcpArgs;

    // 构建MCP服务器的完整路径
    wxString mcpServerPath = wxGetCwd() + wxT("/mcp_file_server.py");
    if (!wxFileExists(mcpServerPath)) {
        // 如果当前目录没有，尝试在可执行文件目录中查找
        wxString exePath = wxStandardPaths::Get().GetExecutablePath();
        wxString exeDir = wxPathOnly(exePath);
        mcpServerPath = exeDir + wxT("/mcp_file_server.py");
    }

    mcpArgs.Add(mcpServerPath);
    mcpArgs.Add(wxT("--allowed-paths"));
    mcpArgs.Add(wxGetCwd());  // 允许访问当前工作目录
    mcpArgs.Add(wxT("/home/<USER>"));  // 允许访问用户主目录
    mcpArgs.Add(wxT("/home/<USER>/Nuctech_Services"));  // 允许访问服务目录

    // 调试信息：显示MCP服务器路径
    // wxLogMessage(wxT("尝试启动MCP服务器: %s"), mcpServerPath);

    bool mcpStarted = m_mcpClient->StartServer(wxT("python3"), mcpArgs);
    if (mcpStarted) {
        // wxLogMessage(wxT("MCP服务器启动成功，开始初始化..."));
        mcpStarted = m_mcpClient->Initialize();
        if (mcpStarted) {
            // wxLogMessage(wxT("MCP服务器初始化成功"));
        } else {
            wxLogMessage(wxT("MCP服务器初始化失败"));
        }
    } else {
        wxLogMessage(wxT("MCP服务器启动失败"));
    }

    if (!mcpStarted) {
        wxString errorMsg = wxString::Format(
            wxT("启动 MCP 文件服务器失败，文件操作功能将不可用。\n\n")
            wxT("尝试的路径: %s\n")
            wxT("请检查:\n")
            wxT("1. Python3是否已安装\n")
            wxT("2. mcp_file_server.py文件是否存在\n")
            wxT("3. 文件是否有执行权限"),
            mcpServerPath);
        wxMessageBox(errorMsg, wxT("MCP启动失败"), wxOK | wxICON_WARNING, this);
    }

    // 启动 Qwen-Agent 服务
    if (StartQwenAgentService()) {
        // 开始检查状态
        m_statusTimer->Start(STATUS_CHECK_INTERVAL);
    } else {
        wxMessageBox(wxT("启动 Qwen-Agent 服务失败，请检查系统环境。\n\n")
                     wxT("请确保：\n")
                     wxT("1. Python3 已安装\n")
                     wxT("2. 已安装 qwen-agent 依赖包\n")
                     wxT("3. start_qwen_service.sh 脚本存在且可执行"),
                     wxT("错误"), wxOK | wxICON_ERROR, this);
        SwitchToState(STATE_CONFIRMATION);
    }
}

void AiChatDialog::OnCancel(wxCommandEvent& WXUNUSED(event))
{
    EndModal(wxID_CANCEL);
}

void AiChatDialog::OnSendMessage(wxCommandEvent& WXUNUSED(event))
{
    if (m_currentState != STATE_CHATTING || m_waitingForResponse) {
        return;
    }
    
    wxString message = m_inputText->GetValue().Trim();
    if (message.IsEmpty()) {
        return;
    }
    
    // 清空输入框
    m_inputText->SetValue(wxEmptyString);
    
    // 添加用户消息到聊天历史
    ChatMessage userMsg(wxT("user"), message);
    m_chatHistory.push_back(userMsg);
    AddMessageToChat(userMsg);
    
    // 发送消息到 Qwen-Agent
    SendMessageToQwenAgent(message);
}

void AiChatDialog::OnInputEnter(wxCommandEvent& event)
{
    OnSendMessage(event);
}

void AiChatDialog::OnClose(wxCloseEvent& WXUNUSED(event))
{
    StopOllama();
    EndModal(wxID_CANCEL);
}

void AiChatDialog::OnTimer(wxTimerEvent& WXUNUSED(event))
{
    if (m_currentState == STATE_STARTING) {
        CheckOllamaStatus();
    } else if (m_waitingForResponse && !m_streamOutputFile.IsEmpty()) {
        // 处理流式响应
        ReadStreamingOutput();
    }
}

bool AiChatDialog::StartOllama()
{
    // 检查是否已经在运行
    if (IsOllamaRunning()) {
        SwitchToState(STATE_CHATTING);
        return true;
    }

    // 启动 Qwen-Agent 服务
    // 注意：我们假设 Qwen-Agent 服务已经在后台运行
    // 这里只是等待服务启动完成
    wxLogMessage(wxT("等待 Qwen-Agent 服务启动..."));

    // 设置一个假的进程ID，表示我们正在等待服务
    m_ollamaPid = 1;

    return true;
}

void AiChatDialog::StopOllama()
{
    if (m_statusTimer && m_statusTimer->IsRunning()) {
        m_statusTimer->Stop();
    }

    // 注意：我们不强制杀死ollama进程，因为它可能被其他应用使用
    // 只是停止我们的监控
    m_ollamaPid = 0;
}

bool AiChatDialog::IsOllamaRunning()
{
    // 使用更简单的方法 - 直接返回 true，因为我们假设服务已经在运行
    // 这是一个临时解决方案，用于测试
    wxLogMessage(wxT("跳过服务检查，直接返回服务可用"));
    return true;
}

void AiChatDialog::CheckOllamaStatus()
{
    if (IsOllamaRunning()) {
        m_statusTimer->Stop();
        SwitchToState(STATE_CHATTING);
    }
}

void AiChatDialog::SendMessageToOllama(const wxString& message)
{
    SendMessageToOllamaWithRetry(message, 0);
}

void AiChatDialog::SendMessageToOllamaWithRetry(const wxString& message, int retryCount)
{
    if (m_waitingForResponse) {
        return;
    }

    m_waitingForResponse = true;

    // 显示重试状态
    if (retryCount == 0) {
        m_statusText->SetLabel(wxT("正在思考..."));
    } else {
        m_statusText->SetLabel(wxString::Format(wxT("重试中... (%d/3)"), retryCount));
    }
    m_sendButton->Enable(false);

    // 创建请求JSON
    wxString jsonData = CreateChatRequestJson(message);

    // 使用流式处理发送HTTP请求
    bool success = SendStreamingHttpRequest(jsonData);

    if (!success) {
        // HTTP请求失败
        if (retryCount < 3) {
            // 重试
            m_waitingForResponse = false;
            wxSleep(2); // 等待2秒
            SendMessageToOllamaWithRetry(message, retryCount + 1);
            return;
        } else {
            // 最终失败
            ChatMessage errorMsg(wxT("assistant"),
                wxT("抱歉，多次尝试后仍无法连接到AI服务。\n\n")
                wxT("请检查：\n")
                wxT("1. Ollama 服务是否正常运行 (ollama serve)\n")
                wxT("2. 模型是否已下载 (ollama pull qwen3:8b)\n")
                wxT("3. 端口 11434 是否可访问\n")
                wxT("4. 网络连接是否稳定\n")
                wxT("5. 系统资源是否充足"));
            m_chatHistory.push_back(errorMsg);
            AddMessageToChat(errorMsg);

            m_waitingForResponse = false;
            m_statusText->SetLabel(wxT("就绪"));
            m_sendButton->Enable(true);
            m_inputText->SetFocus();
        }
    }
}

void AiChatDialog::AddMessageToChat(const ChatMessage& message)
{
    wxString displayText = m_chatDisplay->GetValue();

    // 添加时间戳和角色标识
    wxString roleLabel = (message.role == wxT("user")) ? wxT("您") : wxT("AI");
    wxString timeStr = message.timestamp.Format(wxT("%H:%M:%S"));

    displayText += wxString::Format(wxT("[%s] %s:\n%s\n\n"),
                                   timeStr, roleLabel, message.content);

    m_chatDisplay->SetValue(displayText);

    // 滚动到底部 - 使用更温和的方式
    m_chatDisplay->SetInsertionPointEnd();
    m_chatDisplay->ShowPosition(m_chatDisplay->GetLastPosition());
}

void AiChatDialog::UpdateChatDisplay()
{
    wxString displayText;

    for (const auto& msg : m_chatHistory) {
        wxString roleLabel = (msg.role == wxT("user")) ? wxT("您") : wxT("AI");
        wxString timeStr = msg.timestamp.Format(wxT("%H:%M:%S"));

        displayText += wxString::Format(wxT("[%s] %s:\n%s\n\n"),
                                       timeStr, roleLabel, msg.content);
    }

    m_chatDisplay->SetValue(displayText);
    m_chatDisplay->SetInsertionPointEnd();
    m_chatDisplay->ShowPosition(m_chatDisplay->GetLastPosition());
}

void AiChatDialog::ProcessOllamaResponse(const wxString& response)
{
    // 首先检查响应是否为空或过短
    if (response.IsEmpty() || response.length() < 10) {
        ChatMessage errorMsg(wxT("assistant"), wxT("收到空响应或响应过短，请重试。"));
        m_chatHistory.push_back(errorMsg);
        AddMessageToChat(errorMsg);
        return;
    }

    try {
        // 将wxString转换为std::string用于JSON解析
        std::string jsonStr = response.ToUTF8().data();

        // 预处理JSON字符串，修复常见的格式问题
        jsonStr = PreprocessJsonString(jsonStr);

        // 检查预处理后的字符串是否有效
        if (jsonStr.empty()) {
            wxString errorMsg = wxString::Format(
                wxT("响应严重损坏，无法修复。\n\n")
                wxT("原始响应长度：%d字符\n")
                wxT("可能原因：\n")
                wxT("1. 网络连接不稳定\n")
                wxT("2. Ollama服务过载\n")
                wxT("3. 模型响应异常\n\n")
                wxT("建议：\n")
                wxT("1. 等待几分钟后重试\n")
                wxT("2. 重启Ollama服务\n")
                wxT("3. 发送更简单的问题"),
                (int)response.length());

            ChatMessage errorChatMsg(wxT("assistant"), errorMsg);
            m_chatHistory.push_back(errorChatMsg);
            AddMessageToChat(errorChatMsg);
            return;
        }

        // 使用nlohmann/json解析响应
        json jsonResponse = json::parse(jsonStr);

        // 检查是否有错误字段
        if (jsonResponse.contains("error")) {
            wxString errorMsg = wxString::Format(
                wxT("Ollama API 错误：%s\n\n可能的解决方案：\n")
                wxT("1. 检查 Ollama 服务是否正常运行\n")
                wxT("2. 确认模型 'qwen3:8b' 已下载\n")
                wxT("3. 检查网络连接\n")
                wxT("4. 重启 Ollama 服务"),
                wxString::FromUTF8(jsonResponse["error"].get<std::string>().c_str()));

            ChatMessage errorChatMsg(wxT("assistant"), errorMsg);
            m_chatHistory.push_back(errorChatMsg);
            AddMessageToChat(errorChatMsg);
            return;
        }

        // 检查响应是否完整
        if (!jsonResponse.contains("done") || !jsonResponse["done"].get<bool>()) {
            wxString errorMsg = wxString::Format(
                wxT("收到不完整的响应，可能是网络问题。\n\n")
                wxT("响应长度：%d 字符\n")
                wxT("请重试发送消息。"),
                (int)response.length());

            ChatMessage errorChatMsg(wxT("assistant"), errorMsg);
            m_chatHistory.push_back(errorChatMsg);
            AddMessageToChat(errorChatMsg);
            return;
        }

        // 提取content字段
        if (jsonResponse.contains("message") &&
            jsonResponse["message"].contains("content")) {

            std::string contentStr = jsonResponse["message"]["content"].get<std::string>();
            wxString content = wxString::FromUTF8(contentStr.c_str());

            if (!content.IsEmpty()) {
                // 清理内容（移除过多的空行和特殊标记）
                content = CleanResponseContent(content);

                // 检查是否包含工具调用
                std::vector<MCPToolCall> toolCalls;
                bool hasToolCalls = ParseToolCalls(content, toolCalls);

                // 添加AI回复到聊天历史
                ChatMessage aiMsg(wxT("assistant"), content);
                m_chatHistory.push_back(aiMsg);
                AddMessageToChat(aiMsg);

                // 如果有工具调用，执行它们
                if (hasToolCalls) {
                    wxString toolResults = ExecuteToolCalls(toolCalls);

                    // 添加工具执行结果到聊天历史
                    ChatMessage toolMsg(wxT("assistant"), toolResults);
                    m_chatHistory.push_back(toolMsg);
                    AddMessageToChat(toolMsg);
                }
            } else {
                ChatMessage errorMsg(wxT("assistant"), wxT("AI返回了空内容，请重试您的问题。"));
                m_chatHistory.push_back(errorMsg);
                AddMessageToChat(errorMsg);
            }
        } else {
            // 没有找到content字段
            wxString debugMsg = wxString::Format(
                wxT("响应中没有找到content字段。\n\n")
                wxT("响应结构：%s"),
                wxString::FromUTF8(jsonResponse.dump(2).c_str()).Left(300));
            ChatMessage errorMsg(wxT("assistant"), debugMsg);
            m_chatHistory.push_back(errorMsg);
            AddMessageToChat(errorMsg);
        }

    } catch (const json::parse_error& e) {
        // JSON解析错误 - 提供更详细的诊断信息
        wxString errorMsg;

        // 检查是否是截断问题
        if (response.length() < 100) {
            errorMsg = wxString::Format(
                wxT("收到的响应太短（%d字符），可能是网络问题。\n\n")
                wxT("建议：\n")
                wxT("1. 检查网络连接\n")
                wxT("2. 重试发送消息\n")
                wxT("3. 检查Ollama服务状态"),
                (int)response.length());
        } else if (!response.Contains(wxT("\"done\":true")) && !response.Contains(wxT("\"done\": true"))) {
            errorMsg = wxString::Format(
                wxT("响应未完成，可能是超时或服务器问题。\n\n")
                wxT("响应长度：%d字符\n")
                wxT("响应预览：%s\n\n")
                wxT("建议：\n")
                wxT("1. 等待几秒后重试\n")
                wxT("2. 尝试发送更简短的问题\n")
                wxT("3. 检查Ollama服务负载"),
                (int)response.length(),
                response.Left(150));
        } else {
            errorMsg = wxString::Format(
                wxT("JSON格式错误：%s\n\n")
                wxT("响应长度：%d字符\n")
                wxT("响应预览：%s\n\n")
                wxT("这可能是因为：\n")
                wxT("1. 响应包含特殊字符\n")
                wxT("2. 模型输出格式异常\n")
                wxT("3. 网络传输损坏"),
                wxString::FromUTF8(e.what()),
                (int)response.length(),
                response.Left(200));
        }

        ChatMessage errorChatMsg(wxT("assistant"), errorMsg);
        m_chatHistory.push_back(errorChatMsg);
        AddMessageToChat(errorChatMsg);

    } catch (const std::exception& e) {
        // 其他异常
        wxString errorMsg = wxString::Format(
            wxT("处理响应时发生错误：%s\n\n")
            wxT("请重试您的问题。如果问题持续存在，请检查：\n")
            wxT("1. Ollama服务是否正常运行\n")
            wxT("2. 模型是否正确加载\n")
            wxT("3. 系统资源是否充足"),
            wxString::FromUTF8(e.what()));

        ChatMessage errorChatMsg(wxT("assistant"), errorMsg);
        m_chatHistory.push_back(errorChatMsg);
        AddMessageToChat(errorChatMsg);
    }
}

bool AiChatDialog::SendHttpRequest(const wxString& jsonData, wxString& response)
{
    // 创建临时文件来存储JSON数据，避免shell转义问题
    char tempFileName[] = "/tmp/ollama_request_XXXXXX";
    int fd = mkstemp(tempFileName);
    if (fd == -1) {
        return false;
    }

    // 将wxString转换为UTF-8字节并写入临时文件
    wxCharBuffer utf8Buffer = jsonData.ToUTF8();
    if (!utf8Buffer.data()) {
        close(fd);
        unlink(tempFileName);
        return false;
    }

    ssize_t written = write(fd, utf8Buffer.data(), strlen(utf8Buffer.data()));
    close(fd);

    if (written == -1) {
        unlink(tempFileName);
        return false;
    }

    // 创建输出临时文件
    char outputTempFileName[] = "/tmp/ollama_response_XXXXXX";
    int outputFd = mkstemp(outputTempFileName);
    if (outputFd == -1) {
        unlink(tempFileName);
        return false;
    }
    close(outputFd);

    // 使用curl命令发送HTTP请求，将输出写入文件
    wxString curlCommand = wxString::Format(
        wxT("curl -s -X POST %s -H \"Content-Type: application/json\" --connect-timeout 60 --max-time 300 --retry 3 --retry-delay 2 --data-binary @%s -o %s"),
        OLLAMA_API_URL, wxString(tempFileName, wxConvUTF8), wxString(outputTempFileName, wxConvUTF8));

    // 执行curl命令
    long result = wxExecute(curlCommand, wxEXEC_SYNC);

    // 清理输入临时文件
    unlink(tempFileName);

    if (result == 0) {
        // 从输出文件读取响应
        wxFile responseFile(wxString(outputTempFileName, wxConvUTF8), wxFile::read);
        if (responseFile.IsOpened()) {
            wxString fileContent;
            if (responseFile.ReadAll(&fileContent)) {
                response = fileContent;
            } else {
                unlink(outputTempFileName);
                return false;
            }
            responseFile.Close();
        } else {
            unlink(outputTempFileName);
            return false;
        }

        // 清理输出临时文件
        unlink(outputTempFileName);

        // 检查响应是否完整（更严格的验证）
        bool hasContent = response.Contains(wxT("\"content\":"));
        bool hasDone = response.Contains(wxT("\"done\":true")) || response.Contains(wxT("\"done\": true"));
        bool hasModel = response.Contains(wxT("\"model\":"));
        bool hasMessage = response.Contains(wxT("\"message\":"));
        bool hasValidLength = response.length() > 100; // 至少100字符
        bool endsWithBrace = response.EndsWith(wxT("}"));

        if (hasContent && hasDone && hasModel && hasMessage && hasValidLength && endsWithBrace) {
            return true;
        } else {
            // 响应不完整，记录详细调试信息
            wxLogDebug(wxT("响应验证失败 - 长度:%d, 内容:%s, 完成:%s, 模型:%s, 消息:%s, 结束:%s"),
                      (int)response.length(),
                      hasContent ? wxT("是") : wxT("否"),
                      hasDone ? wxT("是") : wxT("否"),
                      hasModel ? wxT("是") : wxT("否"),
                      hasMessage ? wxT("是") : wxT("否"),
                      endsWithBrace ? wxT("是") : wxT("否"));
            return false;
        }
    }

    return false;
}

bool AiChatDialog::SendStreamingHttpRequest(const wxString& jsonData)
{
    // 使用更简单的方法：直接通过stdin传递数据给curl
    // 初始化流式响应
    m_currentStreamingResponse.Clear();

    // 预先添加助手消息到聊天历史
    ChatMessage assistantMsg(wxT("assistant"), wxT(""));
    m_chatHistory.push_back(assistantMsg);

    // 在聊天显示中添加助手消息的开始
    m_chatDisplay->SetDefaultStyle(wxTextAttr(*wxBLUE));
    m_chatDisplay->AppendText(wxT("\n🤖 AI助手: "));
    m_chatDisplay->SetDefaultStyle(wxTextAttr(*wxBLACK));

    // 创建输出临时文件
    char outputTempFileName[] = "/tmp/ollama_stream_XXXXXX";
    int outputFd = mkstemp(outputTempFileName);
    if (outputFd == -1) {
        return false;
    }
    close(outputFd);

    // 创建输入临时文件，确保正确写入
    wxString tempInputFile = wxT("/tmp/ollama_input_") + wxString::Format(wxT("%ld"), wxGetProcessId()) + wxT(".json");

    // 写入JSON数据到临时文件
    wxFile inputFile(tempInputFile, wxFile::write);
    if (!inputFile.IsOpened()) {
        unlink(outputTempFileName);
        return false;
    }

    // 将JSON数据写入文件
    wxCharBuffer utf8Buffer = jsonData.ToUTF8();
    if (!utf8Buffer.data() || !inputFile.Write(utf8Buffer.data(), strlen(utf8Buffer.data()))) {
        inputFile.Close();
        wxRemoveFile(tempInputFile);
        unlink(outputTempFileName);
        return false;
    }
    inputFile.Close();

    // 使用curl命令发送流式HTTP请求，直接重定向到文件
    wxString outputFile = wxString(outputTempFileName, wxConvUTF8);

    // 构建curl命令，使用绝对路径和正确的重定向
    wxString curlCommand = wxString::Format(
        wxT("curl -s -N -X POST '%s' -H 'Content-Type: application/json' --connect-timeout 60 --max-time 300 --data-binary '@%s'"),
        OLLAMA_API_URL, tempInputFile);

    // 使用bash -c来确保重定向正确工作
    wxString fullCommand = wxString::Format(
        wxT("bash -c \"%s > '%s' 2>/dev/null\" &"),
        curlCommand, outputFile);

    // 执行命令（后台运行）
    long result = wxExecute(fullCommand, wxEXEC_ASYNC);

    // 延迟删除输入文件，给curl时间读取
    wxString cleanupCommand = wxString::Format(wxT("sleep 2; rm -f \"%s\""), tempInputFile);
    wxExecute(cleanupCommand + wxT(" &"), wxEXEC_ASYNC);

    // 启动定时器来定期读取输出文件
    m_streamOutputFile = wxString(outputTempFileName, wxConvUTF8);
    m_streamFilePosition = 0;

    // 创建定时器来读取流式输出
    if (!m_statusTimer->IsRunning()) {
        m_statusTimer->Start(200); // 每200ms检查一次
    }

    return true;
}

void AiChatDialog::ProcessStreamingResponse(const wxString& chunk)
{
    // 处理流式响应的每个chunk
    wxArrayString lines = wxSplit(chunk, '\n');

    for (const wxString& line : lines) {
        wxString trimmedLine = line;
        trimmedLine.Trim().Trim(false);
        if (line.IsEmpty() || trimmedLine.IsEmpty()) continue;

        try {
            // 确保正确的UTF-8编码转换
            wxCharBuffer utf8Buffer = line.ToUTF8();
            if (!utf8Buffer.data()) {
                wxLogDebug(wxT("Failed to convert line to UTF-8: %s"), line);
                continue;
            }

            std::string jsonStr(utf8Buffer.data());

            // 检查是否为有效的JSON开始
            if (jsonStr.empty() || jsonStr[0] != '{') {
                wxLogDebug(wxT("Invalid JSON format: %s"), line);
                continue;
            }

            auto jsonObj = nlohmann::json::parse(jsonStr);

            if (jsonObj.contains("message") && jsonObj["message"].contains("content")) {
                std::string contentStr = jsonObj["message"]["content"].get<std::string>();
                wxString content = wxString::FromUTF8(contentStr.c_str());

                // 将内容添加到当前流式响应
                m_currentStreamingResponse += content;

                // 实时显示内容
                m_chatDisplay->AppendText(content);
                m_chatDisplay->ScrollLines(1);

                // 强制刷新显示
                m_chatDisplay->Update();
                m_chatDisplay->Refresh();
                wxTheApp->Yield();

                // 调试输出
                wxLogDebug(wxT("Streaming content: %s"), content);
            }

            // 检查是否完成
            if (jsonObj.contains("done") && jsonObj["done"].get<bool>()) {
                FinishStreamingResponse();
                break;
            }

        } catch (const std::exception& e) {
            // JSON解析失败，输出更详细的调试信息
            wxLogDebug(wxT("JSON parse error for line '%s': %s"), line, wxString::FromUTF8(e.what()));
            continue;
        }
    }
}

void AiChatDialog::ReadStreamingOutput()
{
    if (m_streamOutputFile.IsEmpty()) {
        return;
    }

    // 检查文件是否存在
    if (!wxFile::Exists(m_streamOutputFile)) {
        return;
    }

    // 获取文件大小
    wxFile tempFile(m_streamOutputFile);
    if (!tempFile.IsOpened()) {
        return;
    }

    long fileSize = tempFile.Length();
    tempFile.Close();

    // 如果文件大小没有变化，检查是否应该结束
    static long lastSize = 0;
    static int unchangedCount = 0;

    if (fileSize == lastSize && fileSize > 0) {
        unchangedCount++;
        if (unchangedCount > 15) { // 3秒没有变化，认为完成
            FinishStreamingResponse();
            unchangedCount = 0;
            lastSize = 0;
            return;
        }
    } else {
        unchangedCount = 0;
        lastSize = fileSize;
    }

    // 如果没有新内容，返回
    if (fileSize <= m_streamFilePosition) {
        return;
    }

    // 调试输出
    wxLogDebug(wxT("Reading streaming output: fileSize=%ld, position=%ld"), fileSize, m_streamFilePosition);

    // 打开文件读取新内容
    wxFile file(m_streamOutputFile, wxFile::read);
    if (!file.IsOpened()) {
        wxLogDebug(wxT("Failed to open output file: %s"), m_streamOutputFile);
        return;
    }

    // 移动到上次读取的位置
    file.Seek(m_streamFilePosition);

    // 读取新内容
    char buffer[4096];
    size_t bytesToRead = std::min((long)sizeof(buffer) - 1, fileSize - m_streamFilePosition);

    if (bytesToRead > 0) {
        size_t bytesRead = file.Read(buffer, bytesToRead);
        if (bytesRead > 0) {
            buffer[bytesRead] = '\0';

            // 确保UTF-8编码正确
            wxString newContent(buffer, wxConvUTF8);

            // 检查转换是否成功
            if (newContent.IsEmpty() && bytesRead > 0) {
                // 尝试使用默认编码
                newContent = wxString(buffer, wxConvLocal);
                wxLogDebug(wxT("UTF-8 conversion failed, using local encoding"));
            }

            // 调试输出
            wxLogDebug(wxT("Read %zu bytes, content length: %zu"), bytesRead, newContent.length());

            // 更新文件位置
            m_streamFilePosition += bytesRead;

            // 处理新内容
            if (!newContent.IsEmpty()) {
                ProcessStreamingResponse(newContent);
            }
        }
    }

    file.Close();
}

void AiChatDialog::FinishStreamingResponse()
{
    // 更新聊天历史中的最后一条助手消息
    if (!m_chatHistory.empty() && m_chatHistory.back().role == wxT("assistant")) {
        m_chatHistory.back().content = m_currentStreamingResponse;
    }

    // 处理工具调用（如果有）
    std::vector<MCPToolCall> toolCalls;
    if (ParseToolCalls(m_currentStreamingResponse, toolCalls)) {
        wxString toolResults = ExecuteToolCalls(toolCalls);
        if (!toolResults.IsEmpty()) {
            m_chatDisplay->AppendText(wxT("\n\n") + toolResults);
        }
    }

    // 清理临时文件
    if (!m_streamOutputFile.IsEmpty()) {
        wxRemoveFile(m_streamOutputFile);
        m_streamOutputFile.Clear();
        m_streamFilePosition = 0;
    }

    // 重置状态
    m_waitingForResponse = false;
    m_statusText->SetLabel(wxT("就绪"));
    m_sendButton->Enable(true);
    m_inputText->SetFocus();

    // 清空当前流式响应
    m_currentStreamingResponse.Clear();
}

wxString AiChatDialog::CreateChatRequestJson(const wxString& userMessage)
{
    // 创建消息数组，包含聊天历史
    wxString messagesJson = wxT("[");

    // 添加系统提示（包含工具信息）
    wxString systemPrompt = wxT("你是一个有用的AI助手。请用中文回答问题。");

    // 如果MCP客户端可用，添加工具描述
    if (m_mcpClient && m_mcpClient->IsServerRunning()) {
        wxString toolsDesc = m_mcpClient->GenerateToolsDescription();
        if (!toolsDesc.IsEmpty()) {
            systemPrompt += wxT("\n\n") + toolsDesc;
        }
    }

    messagesJson += wxString::Format(wxT("{\"role\":\"system\",\"content\":\"%s\"},"),
                                    EscapeJsonString(systemPrompt));

    // 添加最近的聊天历史（限制数量以避免请求过大）
    size_t historyLimit = 10; // 最多包含最近10条消息
    size_t startIndex = (m_chatHistory.size() > historyLimit) ?
                       (m_chatHistory.size() - historyLimit) : 0;

    for (size_t i = startIndex; i < m_chatHistory.size(); i++) {
        const ChatMessage& msg = m_chatHistory[i];
        messagesJson += wxString::Format(
            wxT("{\"role\":\"%s\",\"content\":\"%s\"},"),
            msg.role, EscapeJsonString(msg.content));
    }

    // 添加当前用户消息
    messagesJson += wxString::Format(
        wxT("{\"role\":\"user\",\"content\":\"%s\"}"),
        EscapeJsonString(userMessage));

    messagesJson += wxT("]");

    // 创建完整的请求JSON - 启用流式输出
    wxString requestJson = wxString::Format(
        wxT("{\"model\":\"qwen3:8b\",\"messages\":%s,\"stream\":true,\"options\":{\"temperature\":0.7,\"top_p\":0.9}}"),
        messagesJson);

    return requestJson;
}

wxString AiChatDialog::EscapeJsonString(const wxString& str)
{
    wxString escaped = str;

    // 转义特殊字符
    escaped.Replace(wxT("\\"), wxT("\\\\"));
    escaped.Replace(wxT("\""), wxT("\\\""));
    escaped.Replace(wxT("\n"), wxT("\\n"));
    escaped.Replace(wxT("\r"), wxT("\\r"));
    escaped.Replace(wxT("\t"), wxT("\\t"));

    return escaped;
}

std::string AiChatDialog::PreprocessJsonString(const std::string& jsonStr)
{
    std::string processed = jsonStr;

    // 检查响应长度，如果太短直接返回空
    if (processed.length() < 50) {
        return "";
    }

    // 检查是否包含基本的JSON结构
    if (processed.find("\"model\":") == std::string::npos ||
        processed.find("\"message\":") == std::string::npos) {
        return "";
    }

    // 检查JSON是否看起来完整（有开始和结束大括号，并且包含done字段）
    if (processed.find("\"done\":") == std::string::npos ||
        processed.empty() || processed[0] != '{' || processed.back() != '}') {
        // JSON可能被截断或损坏
        std::string fallbackResponse = "{\"model\":\"qwen3:8b\",\"message\":{\"role\":\"assistant\",\"content\":\"响应被截断或损坏，请重试您的问题。\\n\\n可能的原因：\\n1. 网络连接不稳定\\n2. Ollama服务过载\\n3. 请求超时\\n\\n建议：\\n1. 等待几秒后重试\\n2. 发送更简短的问题\\n3. 检查Ollama服务状态\"},\"done\":true}";
        return fallbackResponse;
    }

    // 首先检查JSON是否完整
    if (processed.empty() || processed.back() != '}') {
        // JSON可能被截断，尝试修复
        size_t lastBrace = processed.rfind('}');
        if (lastBrace != std::string::npos) {
            // 截取到最后一个完整的大括号
            processed = processed.substr(0, lastBrace + 1);
        } else {
            // 没有找到结束大括号，返回fallback响应
            std::string fallbackResponse = "{\"model\":\"qwen3:8b\",\"message\":{\"role\":\"assistant\",\"content\":\"JSON结构损坏，无法解析响应。请重试您的问题。\"},\"done\":true}";
            return fallbackResponse;
        }
    }

    // 查找content字段的值并修复其中的换行符问题
    size_t contentPos = processed.find("\"content\":");
    if (contentPos != std::string::npos) {
        // 找到content字段的值开始位置
        size_t valueStart = processed.find('"', contentPos + 10);
        if (valueStart != std::string::npos) {
            valueStart++; // 跳过开始的引号

            // 找到content字段值的结束位置
            size_t valueEnd = valueStart;
            bool inEscape = false;

            for (size_t i = valueStart; i < processed.length(); i++) {
                char ch = processed[i];
                if (inEscape) {
                    inEscape = false;
                } else if (ch == '\\') {
                    inEscape = true;
                } else if (ch == '"') {
                    valueEnd = i;
                    break;
                }
            }

            if (valueEnd > valueStart) {
                // 提取content字段的值
                std::string contentValue = processed.substr(valueStart, valueEnd - valueStart);

                // 修复content值中的换行符和其他控制字符
                std::string fixedContent = contentValue;

                // 替换未转义的换行符
                size_t pos = 0;
                while ((pos = fixedContent.find('\n', pos)) != std::string::npos) {
                    // 检查前面是否已经有反斜杠转义
                    if (pos == 0 || fixedContent[pos-1] != '\\') {
                        fixedContent.replace(pos, 1, "\\n");
                        pos += 2;
                    } else {
                        pos++;
                    }
                }

                // 替换未转义的回车符
                pos = 0;
                while ((pos = fixedContent.find('\r', pos)) != std::string::npos) {
                    if (pos == 0 || fixedContent[pos-1] != '\\') {
                        fixedContent.replace(pos, 1, "\\r");
                        pos += 2;
                    } else {
                        pos++;
                    }
                }

                // 替换未转义的制表符
                pos = 0;
                while ((pos = fixedContent.find('\t', pos)) != std::string::npos) {
                    if (pos == 0 || fixedContent[pos-1] != '\\') {
                        fixedContent.replace(pos, 1, "\\t");
                        pos += 2;
                    } else {
                        pos++;
                    }
                }

                // 将修复后的content值替换回原JSON
                processed.replace(valueStart, valueEnd - valueStart, fixedContent);
            }
        }
    }

    return processed;
}



wxString AiChatDialog::CleanResponseContent(const wxString& content)
{
    wxString cleaned = content;

    // 移除 <think> 标签及其内容（这些是模型的内部思考过程）
    int thinkStart = cleaned.Find(wxT("<think>"));
    int thinkEnd = cleaned.Find(wxT("</think>"));

    if (thinkStart != wxNOT_FOUND && thinkEnd != wxNOT_FOUND && thinkEnd > thinkStart) {
        // 移除整个 <think>...</think> 块
        cleaned = cleaned.Left(thinkStart) + cleaned.Mid(thinkEnd + 8); // 8 = "</think>".length()
    }

    // 移除开头和结尾的多余空白
    cleaned.Trim(true);  // 移除开头空白
    cleaned.Trim(false); // 移除结尾空白

    // 将多个连续的换行符替换为最多两个换行符
    while (cleaned.Contains(wxT("\n\n\n"))) {
        cleaned.Replace(wxT("\n\n\n"), wxT("\n\n"));
    }

    // 如果内容为空或只包含空白字符，返回默认消息
    if (cleaned.IsEmpty() || cleaned.Strip().IsEmpty()) {
        return wxT("抱歉，我没有生成有效的回复。请重试您的问题。");
    }

    return cleaned;
}

bool AiChatDialog::ParseToolCalls(const wxString& response, std::vector<MCPToolCall>& toolCalls)
{
    toolCalls.clear();

    try {
        // 查找JSON代码块
        int jsonStart = response.Find(wxT("```json"));
        if (jsonStart == wxNOT_FOUND) {
            jsonStart = response.Find(wxT("```"));
            if (jsonStart == wxNOT_FOUND) {
                return false;
            }
        }

        int jsonEnd = response.Mid(jsonStart + 3).Find(wxT("```"));
        if (jsonEnd != wxNOT_FOUND) {
            jsonEnd += jsonStart + 3;
        }
        if (jsonEnd == wxNOT_FOUND) {
            return false;
        }

        // 提取JSON内容
        wxString jsonContent = response.Mid(jsonStart + 7, jsonEnd - jsonStart - 7);
        if (jsonContent.StartsWith(wxT("json\n"))) {
            jsonContent = jsonContent.Mid(5);
        }
        jsonContent.Trim(true).Trim(false);

        // 解析JSON
        std::string jsonStr = jsonContent.ToUTF8().data();
        json jsonObj = json::parse(jsonStr);

        if (!jsonObj.contains("tool_calls") || !jsonObj["tool_calls"].is_array()) {
            return false;
        }

        // 解析工具调用
        for (const auto& toolCall : jsonObj["tool_calls"]) {
            if (toolCall.contains("name") && toolCall.contains("arguments")) {
                wxString name = wxString::FromUTF8(toolCall["name"].get<std::string>().c_str());
                json arguments = toolCall["arguments"];
                wxString callId = wxString::Format(wxT("call_%d"), (int)toolCalls.size());

                toolCalls.emplace_back(name, arguments, callId);
            }
        }

        return !toolCalls.empty();

    } catch (const std::exception& e) {
        wxLogDebug(wxT("Failed to parse tool calls: %s"), wxString::FromUTF8(e.what()));
        return false;
    }
}

wxString AiChatDialog::ExecuteToolCalls(const std::vector<MCPToolCall>& toolCalls)
{
    if (!m_mcpClient || !m_mcpClient->IsServerRunning()) {
        return wxT("MCP服务器未运行，无法执行工具调用。");
    }

    std::vector<MCPToolResult> results;

    for (const auto& toolCall : toolCalls) {
        MCPToolResult result = m_mcpClient->CallTool(toolCall);
        results.push_back(result);
    }

    return FormatToolResults(results);
}

wxString AiChatDialog::FormatToolResults(const std::vector<MCPToolResult>& results)
{
    wxString formatted = wxT("工具执行结果：\n\n");

    for (size_t i = 0; i < results.size(); i++) {
        const auto& result = results[i];

        formatted += wxString::Format(wxT("工具调用 %d:\n"), (int)i + 1);

        if (result.isError) {
            formatted += wxString::Format(wxT("❌ 错误: %s\n"), result.content);
        } else {
            formatted += wxString::Format(wxT("✅ 成功: %s\n"), result.content);
        }

        formatted += wxT("\n");
    }

    return formatted;
}

// Qwen-Agent 服务管理方法

bool AiChatDialog::StartQwenAgentService()
{
    if (IsQwenAgentServiceRunning()) {
        wxLogMessage(wxT("Qwen-Agent 服务已在运行"));
        return true;
    }

    // 启动 Qwen-Agent 服务
    wxString command = wxT("bash start_qwen_service.sh");

    // 创建进程对象
    m_qwenAgentProcess = new wxProcess(nullptr, wxID_ANY);
    m_qwenAgentProcess->Redirect();

    // 启动进程
    m_qwenAgentPid = wxExecute(command, wxEXEC_ASYNC, m_qwenAgentProcess);

    if (m_qwenAgentPid == 0) {
        wxLogError(wxT("无法启动 Qwen-Agent 服务进程"));
        delete m_qwenAgentProcess;
        m_qwenAgentProcess = nullptr;
        return false;
    }

    wxLogMessage(wxT("Qwen-Agent 服务启动中，PID: %ld"), m_qwenAgentPid);

    // 等待服务启动
    wxMilliSleep(3000);

    return true;
}

void AiChatDialog::StopQwenAgentService()
{
    if (m_qwenAgentProcess) {
        if (m_qwenAgentPid > 0) {
            // 尝试优雅关闭
            kill(m_qwenAgentPid, SIGTERM);

            // 等待进程结束
            int status;
            if (waitpid(m_qwenAgentPid, &status, WNOHANG) == 0) {
                // 如果进程还在运行，强制杀死
                wxMilliSleep(1000);
                kill(m_qwenAgentPid, SIGKILL);
                waitpid(m_qwenAgentPid, &status, 0);
            }
        }

        delete m_qwenAgentProcess;
        m_qwenAgentProcess = nullptr;
    }

    m_qwenAgentPid = 0;
}

bool AiChatDialog::IsQwenAgentServiceRunning()
{
    // 通过健康检查接口验证服务状态
    wxString response;

    // 构建简单的 curl 命令进行健康检查
    wxString curlCommand = wxString::Format(
        wxT("curl -s --connect-timeout 2 --max-time 5 %s"),
        QWEN_AGENT_HEALTH_URL);

    // 创建临时文件存储响应
    char tempFileName[] = "/tmp/qwen_health_XXXXXX";
    int tempFd = mkstemp(tempFileName);
    if (tempFd == -1) {
        return false;
    }
    close(tempFd);

    // 执行健康检查
    wxString fullCommand = wxString::Format(
        wxT("%s > %s 2>/dev/null"),
        curlCommand, wxString(tempFileName, wxConvUTF8));

    long result = wxExecute(fullCommand, wxEXEC_SYNC);

    bool isRunning = false;
    if (result == 0) {
        // 读取响应
        wxFile file(wxString(tempFileName, wxConvUTF8));
        if (file.IsOpened()) {
            wxString content;
            file.ReadAll(&content);
            file.Close();

            // 检查响应是否包含成功状态
            if (content.Contains(wxT("\"status\":\"ok\"")) ||
                content.Contains(wxT("\"initialized\":true"))) {
                isRunning = true;
            }
        }
    }

    // 清理临时文件
    unlink(tempFileName);

    return isRunning;
}

void AiChatDialog::CheckQwenAgentServiceStatus()
{
    if (IsQwenAgentServiceRunning()) {
        // 服务已就绪，切换到聊天界面
        SwitchToState(STATE_CHATTING);

        // 创建会话
        if (m_sessionId.IsEmpty()) {
            // 通过 API 创建新会话
            wxString curlCommand = wxString::Format(
                wxT("curl -s -X POST http://127.0.0.1:5000/sessions"));

            char tempFileName[] = "/tmp/qwen_session_XXXXXX";
            int tempFd = mkstemp(tempFileName);
            if (tempFd != -1) {
                close(tempFd);

                wxString fullCommand = wxString::Format(
                    wxT("%s > %s 2>/dev/null"),
                    curlCommand, wxString(tempFileName, wxConvUTF8));

                if (wxExecute(fullCommand, wxEXEC_SYNC) == 0) {
                    wxFile file(wxString(tempFileName, wxConvUTF8));
                    if (file.IsOpened()) {
                        wxString content;
                        file.ReadAll(&content);
                        file.Close();

                        try {
                            json response = json::parse(content.ToStdString());
                            if (response.contains("session_id")) {
                                m_sessionId = wxString::FromUTF8(
                                    response["session_id"].get<std::string>().c_str());
                                wxLogMessage(wxT("创建会话成功: %s"), m_sessionId);
                            }
                        } catch (const std::exception& e) {
                            wxLogError(wxT("解析会话响应失败: %s"),
                                     wxString::FromUTF8(e.what()));
                        }
                    }
                }

                unlink(tempFileName);
            }
        }
    }
}

bool AiChatDialog::SendMessageToQwenAgent(const wxString& message)
{
    if (!IsOllamaRunning()) {
        wxLogError(wxT("Qwen-Agent 服务未运行"));
        return false;
    }

    try {
        // 构建请求数据
        json requestData = {
            {"message", message.ToStdString()},
            {"session_id", m_sessionId.ToStdString()},
            {"stream", false}  // 暂时使用同步模式
        };

        return SendHttpRequestToQwenAgent(requestData);

    } catch (const std::exception& e) {
        wxLogError(wxT("构建请求失败: %s"), wxString::FromUTF8(e.what()));
        return false;
    }
}

bool AiChatDialog::SendHttpRequestToQwenAgent(const json& requestData)
{
    // 创建临时文件存储请求数据
    char inputTempFileName[] = "/tmp/qwen_request_XXXXXX";
    int inputFd = mkstemp(inputTempFileName);
    if (inputFd == -1) {
        wxLogError(wxT("创建临时文件失败"));
        return false;
    }

    // 写入请求数据
    std::string jsonStr = requestData.dump();
    if (write(inputFd, jsonStr.c_str(), jsonStr.length()) == -1) {
        close(inputFd);
        unlink(inputTempFileName);
        wxLogError(wxT("写入请求数据失败"));
        return false;
    }
    close(inputFd);

    // 创建输出临时文件
    char outputTempFileName[] = "/tmp/qwen_response_XXXXXX";
    int outputFd = mkstemp(outputTempFileName);
    if (outputFd == -1) {
        unlink(inputTempFileName);
        wxLogError(wxT("创建输出临时文件失败"));
        return false;
    }
    close(outputFd);

    // 构建 curl 命令
    wxString curlCommand = wxString::Format(
        wxT("curl -s -X POST %s -H \"Content-Type: application/json\" ")
        wxT("--connect-timeout 30 --max-time 120 --data-binary @%s -o %s"),
        QWEN_AGENT_API_URL,
        wxString(inputTempFileName, wxConvUTF8),
        wxString(outputTempFileName, wxConvUTF8));

    // 执行请求
    m_waitingForResponse = true;
    m_statusText->SetLabel(wxT("AI思考中..."));
    m_sendButton->Enable(false);

    long result = wxExecute(curlCommand, wxEXEC_SYNC);

    // 清理输入文件
    unlink(inputTempFileName);

    if (result == 0) {
        // 读取响应
        wxFile responseFile(wxString(outputTempFileName, wxConvUTF8));
        if (responseFile.IsOpened()) {
            wxString response;
            responseFile.ReadAll(&response);
            responseFile.Close();

            if (!response.IsEmpty()) {
                ProcessQwenAgentResponse(response);

                // 清理输出文件
                unlink(outputTempFileName);

                m_waitingForResponse = false;
                m_statusText->SetLabel(wxT("就绪"));
                m_sendButton->Enable(true);
                m_inputText->SetFocus();

                return true;
            }
        }
    }

    // 清理输出文件
    unlink(outputTempFileName);

    // 请求失败
    m_waitingForResponse = false;
    m_statusText->SetLabel(wxT("就绪"));
    m_sendButton->Enable(true);
    m_inputText->SetFocus();

    ChatMessage errorMsg(wxT("assistant"),
        wxT("抱歉，无法连接到 Qwen-Agent 服务。\n\n")
        wxT("请检查：\n")
        wxT("1. Qwen-Agent 服务是否正常运行\n")
        wxT("2. 端口 5000 是否可访问\n")
        wxT("3. 网络连接是否稳定"));
    m_chatHistory.push_back(errorMsg);
    AddMessageToChat(errorMsg);

    return false;
}

void AiChatDialog::ProcessQwenAgentResponse(const wxString& response)
{
    try {
        json responseJson = json::parse(response.ToStdString());

        if (responseJson.contains("error")) {
            wxString errorMsg = wxString::FromUTF8(
                responseJson["error"].get<std::string>().c_str());

            ChatMessage aiMsg(wxT("assistant"),
                wxString::Format(wxT("处理请求时出现错误：%s"), errorMsg));
            m_chatHistory.push_back(aiMsg);
            AddMessageToChat(aiMsg);
            return;
        }

        if (responseJson.contains("response") && responseJson["response"].contains("content")) {
            wxString content = wxString::FromUTF8(
                responseJson["response"]["content"].get<std::string>().c_str());

            if (!content.IsEmpty()) {
                // 清理内容
                content = CleanResponseContent(content);

                // 添加AI回复到聊天历史
                ChatMessage aiMsg(wxT("assistant"), content);
                m_chatHistory.push_back(aiMsg);
                AddMessageToChat(aiMsg);
            } else {
                ChatMessage errorMsg(wxT("assistant"), wxT("AI返回了空内容，请重试您的问题。"));
                m_chatHistory.push_back(errorMsg);
                AddMessageToChat(errorMsg);
            }
        } else {
            ChatMessage errorMsg(wxT("assistant"), wxT("收到无效的响应格式。"));
            m_chatHistory.push_back(errorMsg);
            AddMessageToChat(errorMsg);
        }

    } catch (const json::parse_error& e) {
        wxLogError(wxT("解析响应JSON失败: %s"), wxString::FromUTF8(e.what()));

        ChatMessage errorMsg(wxT("assistant"),
            wxT("解析AI响应时出现错误，请重试。"));
        m_chatHistory.push_back(errorMsg);
        AddMessageToChat(errorMsg);
    } catch (const std::exception& e) {
        wxLogError(wxT("处理响应时出现异常: %s"), wxString::FromUTF8(e.what()));

        ChatMessage errorMsg(wxT("assistant"),
            wxT("处理AI响应时出现异常，请重试。"));
        m_chatHistory.push_back(errorMsg);
        AddMessageToChat(errorMsg);
    }
}


