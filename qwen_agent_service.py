#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen-Agent 服务层
为 MiniTool C++ 应用提供 AI 聊天和工具调用服务
"""

import os
import json
import asyncio
import logging
from typing import List, Dict, Any, Optional
from flask import Flask, request, jsonify, Response, stream_with_context
from flask_cors import CORS
import threading
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('qwen_agent_service.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

try:
    from qwen_agent.agents import Assistant
    from qwen_agent.tools.base import BaseTool, register_tool
    import json5
except ImportError as e:
    logger.error(f"导入 Qwen-Agent 失败: {e}")
    logger.error("请安装 Qwen-Agent: pip install qwen-agent[gui,rag,code_interpreter,mcp]")
    exit(1)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

class QwenAgentService:
    """Qwen-Agent 服务封装类"""
    
    def __init__(self):
        self.agent = None
        self.is_initialized = False
        self.chat_sessions = {}  # 存储多个聊天会话
        self.init_agent()
    
    def init_agent(self):
        """初始化 Qwen-Agent"""
        try:
            # LLM 配置 - 使用本地 Ollama 服务
            llm_cfg = {
                'model': 'qwen3:8b',  # 与您现有配置保持一致
                'model_server': 'http://localhost:11434/v1',  # Ollama OpenAI 兼容接口
                'api_key': 'EMPTY',
                'generate_cfg': {
                    'top_p': 0.8,
                    'temperature': 0.7,
                    'max_tokens': 2000,
                }
            }
            
            # 工具配置
            tools = [
                {
                    'mcpServers': {
                        'filesystem': {
                            'command': 'python3',
                            'args': ['mcp_file_server.py']  # 使用您现有的 MCP 服务器
                        }
                    }
                },
                'code_interpreter'  # 内置代码解释器
            ]
            
            # 系统提示
            system_message = """你是 MiniTool 的智能助手。你可以：
1. 回答用户的问题
2. 执行文件操作（读取、写入、列出文件等）
3. 运行和解释代码
4. 帮助用户完成各种任务

请用中文回答，保持友好和专业的语调。"""
            
            # 创建 Agent
            self.agent = Assistant(
                llm=llm_cfg,
                function_list=tools,
                name='MiniTool AI Assistant',
                description='MiniTool 集成的智能助手',
                system_message=system_message
            )
            
            self.is_initialized = True
            logger.info("Qwen-Agent 初始化成功")
            
        except Exception as e:
            logger.error(f"初始化 Qwen-Agent 失败: {e}")
            self.is_initialized = False
    
    def create_session(self, session_id: str = None) -> str:
        """创建新的聊天会话"""
        if not session_id:
            session_id = f"session_{int(time.time())}"
        
        self.chat_sessions[session_id] = {
            'messages': [],
            'created_at': datetime.now(),
            'last_activity': datetime.now()
        }
        
        logger.info(f"创建聊天会话: {session_id}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict]:
        """获取聊天会话"""
        return self.chat_sessions.get(session_id)
    
    def update_session_activity(self, session_id: str):
        """更新会话活动时间"""
        if session_id in self.chat_sessions:
            self.chat_sessions[session_id]['last_activity'] = datetime.now()
    
    def chat(self, message: str, session_id: str = None, stream: bool = False):
        """处理聊天请求"""
        if not self.is_initialized:
            raise Exception("Agent 未初始化")
        
        # 获取或创建会话
        if not session_id:
            session_id = self.create_session()
        elif session_id not in self.chat_sessions:
            self.create_session(session_id)
        
        session = self.get_session(session_id)
        self.update_session_activity(session_id)
        
        # 添加用户消息
        user_message = {'role': 'user', 'content': message}
        session['messages'].append(user_message)
        
        try:
            # 调用 Agent
            messages_for_agent = session['messages'].copy()
            
            if stream:
                return self._stream_chat(messages_for_agent, session_id)
            else:
                return self._sync_chat(messages_for_agent, session_id)
                
        except Exception as e:
            logger.error(f"聊天处理失败: {e}")
            error_response = {
                'role': 'assistant',
                'content': f'抱歉，处理您的请求时出现错误：{str(e)}'
            }
            session['messages'].append(error_response)
            return error_response
    
    def _sync_chat(self, messages: List[Dict], session_id: str) -> Dict:
        """同步聊天处理"""
        response_messages = []
        
        for response in self.agent.run(messages=messages):
            response_messages.extend(response)
        
        # 更新会话
        session = self.get_session(session_id)
        session['messages'].extend(response_messages)
        
        # 返回最后一条助手消息
        assistant_messages = [msg for msg in response_messages if msg.get('role') == 'assistant']
        return assistant_messages[-1] if assistant_messages else {'role': 'assistant', 'content': '没有收到回复'}
    
    def _stream_chat(self, messages: List[Dict], session_id: str):
        """流式聊天处理"""
        def generate():
            try:
                response_messages = []
                current_content = ""
                
                for response in self.agent.run(messages=messages):
                    response_messages.extend(response)
                    
                    # 查找最新的助手消息
                    for msg in response:
                        if msg.get('role') == 'assistant':
                            content = msg.get('content', '')
                            if content and content != current_content:
                                # 发送增量内容
                                delta = content[len(current_content):]
                                current_content = content
                                
                                chunk_data = {
                                    'type': 'content',
                                    'delta': delta,
                                    'content': content
                                }
                                yield f"data: {json.dumps(chunk_data, ensure_ascii=False)}\n\n"
                
                # 更新会话
                session = self.get_session(session_id)
                session['messages'].extend(response_messages)
                
                # 发送完成信号
                yield f"data: {json.dumps({'type': 'done'}, ensure_ascii=False)}\n\n"
                
            except Exception as e:
                logger.error(f"流式聊天处理失败: {e}")
                error_data = {
                    'type': 'error',
                    'error': str(e)
                }
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
        
        return generate()
    
    def get_chat_history(self, session_id: str) -> List[Dict]:
        """获取聊天历史"""
        session = self.get_session(session_id)
        return session['messages'] if session else []
    
    def clear_chat_history(self, session_id: str) -> bool:
        """清空聊天历史"""
        if session_id in self.chat_sessions:
            self.chat_sessions[session_id]['messages'] = []
            return True
        return False

# 全局服务实例
qwen_service = QwenAgentService()

# API 路由定义
@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'ok',
        'initialized': qwen_service.is_initialized,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/chat', methods=['POST'])
def chat():
    """聊天接口"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据为空'}), 400
        
        message = data.get('message', '').strip()
        if not message:
            return jsonify({'error': '消息内容为空'}), 400
        
        session_id = data.get('session_id')
        stream = data.get('stream', False)
        
        if stream:
            # 流式响应
            def generate():
                yield "data: " + json.dumps({'type': 'start'}, ensure_ascii=False) + "\n\n"
                for chunk in qwen_service.chat(message, session_id, stream=True):
                    yield chunk
            
            return Response(
                stream_with_context(generate()),
                mimetype='text/event-stream',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Access-Control-Allow-Origin': '*'
                }
            )
        else:
            # 同步响应
            response = qwen_service.chat(message, session_id, stream=False)
            return jsonify({
                'success': True,
                'response': response,
                'session_id': session_id
            })
    
    except Exception as e:
        logger.error(f"聊天接口错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/sessions', methods=['POST'])
def create_session():
    """创建新会话"""
    session_id = qwen_service.create_session()
    return jsonify({
        'success': True,
        'session_id': session_id
    })

@app.route('/sessions/<session_id>/history', methods=['GET'])
def get_history(session_id):
    """获取会话历史"""
    history = qwen_service.get_chat_history(session_id)
    return jsonify({
        'success': True,
        'history': history
    })

@app.route('/sessions/<session_id>/clear', methods=['POST'])
def clear_history(session_id):
    """清空会话历史"""
    success = qwen_service.clear_chat_history(session_id)
    return jsonify({
        'success': success
    })

if __name__ == '__main__':
    logger.info("启动 Qwen-Agent 服务...")
    
    # 检查依赖
    if not qwen_service.is_initialized:
        logger.error("服务初始化失败，退出")
        exit(1)
    
    # 启动服务
    app.run(
        host='127.0.0.1',
        port=5000,
        debug=False,
        threaded=True
    )
