#!/usr/bin/env python3

import asyncio
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入MCP服务器类
from mcp_file_server import MCPFileServer

async def test_write_file():
    print("=== Testing MCP File Server ===")
    print(f"Current directory: {os.getcwd()}")
    print(f"Directory contents: {os.listdir('.')}")
    
    # 创建服务器实例
    allowed_paths = [os.getcwd()]
    server = MCPFileServer(allowed_paths)
    
    print(f"Allowed paths: {server.allowed_paths}")
    
    # 测试参数
    args = {
        'path': 'test.txt',
        'content': 'hello'
    }
    
    print(f"Testing with args: {args}")
    
    try:
        # 测试路径检查
        path = args['path']
        print(f"Testing path: '{path}'")
        print(f"os.path.dirname('{path}') = '{os.path.dirname(path)}'")
        print(f"os.path.abspath('{path}') = '{os.path.abspath(path)}'")
        
        is_allowed = server.is_path_allowed(path)
        print(f"Path allowed: {is_allowed}")
        
        if is_allowed:
            result = await server.write_file(args)
            print('Success:', result)
            
            # 检查文件是否创建
            if os.path.exists('test.txt'):
                with open('test.txt', 'r') as f:
                    content = f.read()
                print(f"File created successfully with content: '{content}'")
            else:
                print("File was not created!")
        else:
            print("Path not allowed!")
            
    except Exception as e:
        print('Error:', e)
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_write_file())
