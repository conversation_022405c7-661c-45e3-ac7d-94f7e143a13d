#ifndef AICHATDIALOG_H
#define AICHATDIALOG_H

#include <wx/wx.h>
#include <wx/process.h>
#include <wx/stream.h>
#include <wx/txtstrm.h>
#include <wx/timer.h>
#include <wx/url.h>
#include <wx/protocol/http.h>
#include <wx/file.h>
#include <vector>
#include "json.hpp"
#include "MCPClient.h"

// 聊天消息结构
struct ChatMessage {
    wxString role;      // "user" 或 "assistant"
    wxString content;   // 消息内容
    wxDateTime timestamp;
    
    ChatMessage(const wxString& r, const wxString& c) 
        : role(r), content(c), timestamp(wxDateTime::Now()) {}
};

class AiChatDialog : public wxDialog
{
public:
    AiChatDialog(wxWindow* parent);
    ~AiChatDialog();

private:
    // 对话框状态
    enum DialogState {
        STATE_CONFIRMATION,  // 确认页面
        STATE_STARTING,      // 启动Ollama中
        STATE_CHATTING       // 聊天界面
    };

    // 事件处理
    void OnConfirm(wxCommandEvent& event);
    void OnCancel(wxCommandEvent& event);
    void OnSendMessage(wxCommandEvent& event);
    void OnInputEnter(wxCommandEvent& event);
    void OnClose(wxCloseEvent& event);
    void OnTimer(wxTimerEvent& event);

    // UI创建方法
    void CreateConfirmationPage();
    void CreateChatPage();
    void SwitchToState(DialogState newState);

    // Ollama管理
    bool StartOllama();
    void StopOllama();
    bool IsOllamaRunning();
    bool IsQwenAgentRunning();
    void CheckOllamaStatus();

    // 聊天功能
    void SendMessageToOllama(const wxString& message);
    void SendMessageToOllamaWithRetry(const wxString& message, int retryCount);
    void AddMessageToChat(const ChatMessage& message);
    void UpdateChatDisplay();
    void ProcessOllamaResponse(const wxString& response);

    // HTTP请求
    bool SendHttpRequest(const wxString& jsonData, wxString& response);
    bool SendStreamingHttpRequest(const wxString& jsonData);
    void ProcessStreamingResponse(const wxString& chunk);
    void FinishStreamingResponse();
    void ReadStreamingOutput();
    wxString CreateChatRequestJson(const wxString& userMessage);
    wxString EscapeJsonString(const wxString& str);

    // 响应处理辅助函数
    std::string PreprocessJsonString(const std::string& jsonStr);
    wxString CleanResponseContent(const wxString& content);

    // MCP工具调用相关
    bool ParseToolCalls(const wxString& response, std::vector<MCPToolCall>& toolCalls);
    wxString ExecuteToolCalls(const std::vector<MCPToolCall>& toolCalls);
    wxString FormatToolResults(const std::vector<MCPToolResult>& results);

    // UI组件 - 确认页面
    wxPanel* m_confirmPanel;
    wxStaticText* m_warningTitle;
    wxStaticText* m_warningText;
    wxButton* m_confirmButton;
    wxButton* m_cancelButton;

    // UI组件 - 聊天页面
    wxPanel* m_chatPanel;
    wxTextCtrl* m_chatDisplay;
    wxTextCtrl* m_inputText;
    wxButton* m_sendButton;
    wxStaticText* m_statusText;

    // 状态管理
    DialogState m_currentState;
    wxTimer* m_statusTimer;
    wxProcess* m_ollamaProcess;
    long m_ollamaPid;

    // 聊天数据
    std::vector<ChatMessage> m_chatHistory;
    bool m_waitingForResponse;

    // 流式处理相关
    wxString m_currentStreamingResponse;
    wxTextAttr m_assistantTextAttr;
    wxString m_streamOutputFile;
    long m_streamFilePosition;

    // MCP客户端
    std::unique_ptr<MCPClient> m_mcpClient;

    // Qwen-Agent 服务相关
    bool StartQwenAgentService();
    void StopQwenAgentService();
    bool IsQwenAgentServiceRunning();
    void CheckQwenAgentServiceStatus();
    bool SendMessageToQwenAgent(const wxString& message);
    bool SendHttpRequestToQwenAgent(const json& requestData);
    void ProcessQwenAgentResponse(const wxString& response);

    // Qwen-Agent 服务进程管理
    wxProcess* m_qwenAgentProcess;
    long m_qwenAgentPid;
    wxString m_sessionId;  // 会话ID

    // 常量
    static const wxString OLLAMA_COMMAND;
    static const wxString OLLAMA_API_URL;
    static const wxString QWEN_AGENT_API_URL;
    static const wxString QWEN_AGENT_HEALTH_URL;
    static const int STATUS_CHECK_INTERVAL;

    wxDECLARE_EVENT_TABLE();
};

// 事件ID
enum
{
    ID_CONFIRM_AI = 2001,
    ID_CANCEL_AI = 2002,
    ID_SEND_MESSAGE = 2003,
    ID_INPUT_TEXT = 2004,
    ID_STATUS_TIMER = 2005
};

#endif // AICHATDIALOG_H
