#include "ReplaceAlgorithmDialog.h"
#include <wx/msgdlg.h>
#include <wx/filename.h>
#include <wx/progdlg.h>
#include <wx/utils.h>
#include <wx/thread.h>
#include <wx/dir.h>

// Define the destination paths
const wxString ReplaceAlgorithmDialog::DESTINATION_PATH = wxT("/home/<USER>/Nuctech_Services/OSS_MR/src/mgsPkg/src");
const wxString ReplaceAlgorithmDialog::ALGORITHM_DATA_DESTINATION = wxT("/home/<USER>/Nuctech_Services/OSS_MR/Release/lib/mgspkg");

wxBEGIN_EVENT_TABLE(ReplaceAlgorithmDialog, wxDialog)
    EVT_BUTTON(ID_OK_BTN, ReplaceAlgorithmDialog::OnOK)
    EVT_BUTTON(ID_CANCEL_BTN, ReplaceAlgorithmDialog::OnCancel)
    EVT_FILEPICKER_CHANGED(ID_MGS_FLOW_PICKER, ReplaceAlgorithmDialog::OnFileChanged)
    EVT_FILEPICKER_CHANGED(ID_CIMG_PICKER, ReplaceAlgorithmDialog::OnFileChanged)
    EVT_FILEPICKER_CHANGED(ID_CLASSIFICATION_PICKER, ReplaceAlgorithmDialog::OnFileChanged)
    EVT_FILEPICKER_CHANGED(ID_GENERAL_PICKER, ReplaceAlgorithmDialog::OnFileChanged)
    EVT_FILEPICKER_CHANGED(ID_GENERAL_H_PICKER, ReplaceAlgorithmDialog::OnFileChanged)
    EVT_FILEPICKER_CHANGED(ID_PROCESS_PICKER, ReplaceAlgorithmDialog::OnFileChanged)
    EVT_DIRPICKER_CHANGED(ID_ALGORITHM_DATA_PICKER, ReplaceAlgorithmDialog::OnDirChanged)
    EVT_CHECKBOX(ID_DEBUG_DISK_CHECKBOX, ReplaceAlgorithmDialog::OnOptionChanged)
    EVT_CHECKBOX(ID_ALGORITHM_RECOMPILE_CHECKBOX, ReplaceAlgorithmDialog::OnOptionChanged)
wxEND_EVENT_TABLE()

ReplaceAlgorithmDialog::ReplaceAlgorithmDialog(wxWindow* parent)
    : wxDialog(parent, wxID_ANY, wxT("替换算法文件"), wxDefaultPosition, wxSize(600, 650))
{
    // Create main sizer
    wxBoxSizer* mainSizer = new wxBoxSizer(wxVERTICAL);

    // // Title
    // wxStaticText* titleText = new wxStaticText(this, wxID_ANY, wxT("算法文件替换"));
    // wxFont titleFont = titleText->GetFont();
    // titleFont.SetPointSize(16);
    // titleFont.SetWeight(wxFONTWEIGHT_BOLD);
    // titleText->SetFont(titleFont);
    // mainSizer->Add(titleText, 0, wxALL | wxCENTER, 15);
    
    // Description
    wxStaticText* descText = new wxStaticText(this, wxID_ANY,
        wxT("请选择替换模式，相应文件将被复制到：\n") + DESTINATION_PATH);
    descText->SetForegroundColour(wxColour(100, 100, 100));
    mainSizer->Add(descText, 0, wxALL | wxEXPAND, 15);
    
    // 选项区：调试盘替换/算法重编
    wxBoxSizer* optionSizer = new wxBoxSizer(wxHORIZONTAL);
    m_debugDiskCheckBox = new wxCheckBox(this, ID_DEBUG_DISK_CHECKBOX, wxT("调试盘替换"));
    m_algorithmRecompileCheckBox = new wxCheckBox(this, ID_ALGORITHM_RECOMPILE_CHECKBOX, wxT("算法重编"));
    optionSizer->Add(m_debugDiskCheckBox, 0, wxALL, 3);
    optionSizer->Add(m_algorithmRecompileCheckBox, 0, wxALL, 3);
    mainSizer->Add(optionSizer, 0, wxALL | wxCENTER, 3);

    // File selection grid
    wxFlexGridSizer* gridSizer = new wxFlexGridSizer(7, 2, 10, 10);
    gridSizer->AddGrowableCol(1, 1);
    
    // 设置默认路径
    wxString defaultPath = wxT("/home/<USER>/Nuctech_Services/OSS_MR/src/mgsPkg/");
    
    // mgs_flow.cpp
    wxStaticText* mgsFlowLabel = new wxStaticText(this, wxID_ANY, wxT("mgs_flow.cpp:"));
    m_mgsFlowPicker = new wxFilePickerCtrl(this, ID_MGS_FLOW_PICKER, wxEmptyString,
        wxT("选择 mgs_flow.cpp 文件"), wxT("C++ files (*.cpp)|*.cpp"),
        wxDefaultPosition, wxDefaultSize, wxFLP_OPEN | wxFLP_FILE_MUST_EXIST);
    m_mgsFlowPicker->SetInitialDirectory(defaultPath);
    gridSizer->Add(mgsFlowLabel, 0, wxALIGN_CENTER_VERTICAL);
    gridSizer->Add(m_mgsFlowPicker, 1, wxEXPAND);
    
    // CImgDataBuffer.cpp
    wxStaticText* cImgLabel = new wxStaticText(this, wxID_ANY, wxT("CImgDataBuffer.cpp:"));
    m_cImgDataBufferPicker = new wxFilePickerCtrl(this, ID_CIMG_PICKER, wxEmptyString,
        wxT("选择 CImgDataBuffer.cpp 文件"), wxT("C++ files (*.cpp)|*.cpp"),
        wxDefaultPosition, wxDefaultSize, wxFLP_OPEN | wxFLP_FILE_MUST_EXIST);
    m_cImgDataBufferPicker->SetInitialDirectory(defaultPath);
    gridSizer->Add(cImgLabel, 0, wxALIGN_CENTER_VERTICAL);
    gridSizer->Add(m_cImgDataBufferPicker, 1, wxEXPAND);
    
    // ClassificationProcess.cpp
    wxStaticText* classificationLabel = new wxStaticText(this, wxID_ANY, wxT("ClassificationProcess.cpp:"));
    m_classificationProcessPicker = new wxFilePickerCtrl(this, ID_CLASSIFICATION_PICKER, wxEmptyString,
        wxT("选择 ClassificationProcess.cpp 文件"), wxT("C++ files (*.cpp)|*.cpp"),
        wxDefaultPosition, wxDefaultSize, wxFLP_OPEN | wxFLP_FILE_MUST_EXIST);
    m_classificationProcessPicker->SetInitialDirectory(defaultPath);
    gridSizer->Add(classificationLabel, 0, wxALIGN_CENTER_VERTICAL);
    gridSizer->Add(m_classificationProcessPicker, 1, wxEXPAND);
    
    // GeneralProcess.cpp
    wxStaticText* generalLabel = new wxStaticText(this, wxID_ANY, wxT("GeneralProcess.cpp:"));
    m_generalProcessPicker = new wxFilePickerCtrl(this, ID_GENERAL_PICKER, wxEmptyString,
        wxT("选择 GeneralProcess.cpp 文件"), wxT("C++ files (*.cpp)|*.cpp"),
        wxDefaultPosition, wxDefaultSize, wxFLP_OPEN | wxFLP_FILE_MUST_EXIST);
    m_generalProcessPicker->SetInitialDirectory(defaultPath);
    gridSizer->Add(generalLabel, 0, wxALIGN_CENTER_VERTICAL);
    gridSizer->Add(m_generalProcessPicker, 1, wxEXPAND);

    // GeneralProcess.h
    wxStaticText* generalHLabel = new wxStaticText(this, wxID_ANY, wxT("GeneralProcess.h:"));
    m_generalProcessHPicker = new wxFilePickerCtrl(this, ID_GENERAL_H_PICKER, wxEmptyString,
        wxT("选择 GeneralProcess.h 文件"), wxT("Header files (*.h)|*.h"),
        wxDefaultPosition, wxDefaultSize, wxFLP_OPEN | wxFLP_FILE_MUST_EXIST);
    m_generalProcessHPicker->SetInitialDirectory(defaultPath);
    gridSizer->Add(generalHLabel, 0, wxALIGN_CENTER_VERTICAL);
    gridSizer->Add(m_generalProcessHPicker, 1, wxEXPAND);

    // process.cpp
    wxStaticText* processLabel = new wxStaticText(this, wxID_ANY, wxT("process.cpp:"));
    m_processPicker = new wxFilePickerCtrl(this, ID_PROCESS_PICKER, wxEmptyString,
        wxT("选择 process.cpp 文件"), wxT("C++ files (*.cpp)|*.cpp"),
        wxDefaultPosition, wxDefaultSize, wxFLP_OPEN | wxFLP_FILE_MUST_EXIST);
    m_processPicker->SetInitialDirectory(defaultPath);
    gridSizer->Add(processLabel, 0, wxALIGN_CENTER_VERTICAL);
    gridSizer->Add(m_processPicker, 1, wxEXPAND);

    // Algorithm Data Folder
    wxStaticText* algorithmDataLabel = new wxStaticText(this, wxID_ANY, wxT("AlgorithmData文件夹:"));
    m_algorithmDataPicker = new wxDirPickerCtrl(this, ID_ALGORITHM_DATA_PICKER, wxEmptyString,
        wxT("选择算法数据文件夹"), wxDefaultPosition, wxDefaultSize, wxDIRP_DIR_MUST_EXIST);
    m_algorithmDataPicker->SetInitialDirectory(wxT("/home/<USER>"));
    gridSizer->Add(algorithmDataLabel, 0, wxALIGN_CENTER_VERTICAL);
    gridSizer->Add(m_algorithmDataPicker, 1, wxEXPAND);

    mainSizer->Add(gridSizer, 1, wxALL | wxEXPAND, 15);
    
    // Warning text
    wxStaticText* warningText = new wxStaticText(this, wxID_ANY,
        wxT("⚠️ 注意：此操作将覆盖目标目录中的现有文件，请确保已备份重要文件。"));
    warningText->SetForegroundColour(wxColour(200, 100, 0));
    mainSizer->Add(warningText, 0, wxALL | wxEXPAND, 15);
    
    // Buttons
    wxBoxSizer* buttonSizer = new wxBoxSizer(wxHORIZONTAL);
    m_okButton = new wxButton(this, ID_OK_BTN, wxT("开始替换并编译"));
    m_cancelButton = new wxButton(this, ID_CANCEL_BTN, wxT("取消"));
    
    m_okButton->SetBackgroundColour(wxColour(220, 240, 255));
    m_cancelButton->SetBackgroundColour(wxColour(240, 240, 240));
    m_okButton->Enable(false); // Initially disabled
    
    buttonSizer->Add(m_cancelButton, 0, wxRIGHT, 10);
    buttonSizer->Add(m_okButton, 0);
    
    mainSizer->Add(buttonSizer, 0, wxALL | wxCENTER, 15);
    
    SetSizer(mainSizer);
    SetMinSize(wxSize(600, 650));
    Centre();
    // 初始化：未勾选时禁用所有文件选择框
    UpdateFilePickersEnable();
}

void ReplaceAlgorithmDialog::OnFileChanged(wxFileDirPickerEvent& event)
{
    // Update file paths
    m_mgsFlowPath = m_mgsFlowPicker->GetPath();
    m_cImgDataBufferPath = m_cImgDataBufferPicker->GetPath();
    m_classificationProcessPath = m_classificationProcessPicker->GetPath();
    m_generalProcessPath = m_generalProcessPicker->GetPath();
    m_generalProcessHPath = m_generalProcessHPicker->GetPath();
    m_processPath = m_processPicker->GetPath();

    // Enable OK button only if at least one file/folder is selected
    bool hasFiles = !m_mgsFlowPath.IsEmpty() || !m_cImgDataBufferPath.IsEmpty() ||
                   !m_classificationProcessPath.IsEmpty() || !m_generalProcessPath.IsEmpty() ||
                   !m_generalProcessHPath.IsEmpty() || !m_processPath.IsEmpty() ||
                   !m_algorithmDataPath.IsEmpty();
    m_okButton->Enable(hasFiles);
}

void ReplaceAlgorithmDialog::OnDirChanged(wxFileDirPickerEvent& event)
{
    // Update algorithm data folder path
    m_algorithmDataPath = m_algorithmDataPicker->GetPath();

    // Enable OK button only if at least one file/folder is selected
    bool hasFiles = !m_mgsFlowPath.IsEmpty() || !m_cImgDataBufferPath.IsEmpty() ||
                   !m_classificationProcessPath.IsEmpty() || !m_generalProcessPath.IsEmpty() ||
                   !m_generalProcessHPath.IsEmpty() || !m_processPath.IsEmpty() ||
                   !m_algorithmDataPath.IsEmpty();
    m_okButton->Enable(hasFiles);
}

void ReplaceAlgorithmDialog::OnOK(wxCommandEvent& WXUNUSED(event))
{
    if (!ValidateFiles()) {
        return;
    }

    // Confirm the operation
    wxString message = wxT("确认要替换以下文件吗？\n\n");
    if (!m_mgsFlowPath.IsEmpty()) {
        message += wxT("• mgs_flow.cpp\n");
    }
    if (!m_cImgDataBufferPath.IsEmpty()) {
        message += wxT("• CImgDataBuffer.cpp\n");
    }
    if (!m_classificationProcessPath.IsEmpty()) {
        message += wxT("• ClassificationProcess.cpp\n");
    }
    if (!m_generalProcessPath.IsEmpty()) {
        message += wxT("• GeneralProcess.cpp\n");
    }
    if (!m_generalProcessHPath.IsEmpty()) {
        message += wxT("• GeneralProcess.h\n");
    }
    if (!m_processPath.IsEmpty()) {
        message += wxT("• process.cpp\n");
    }
    message += wxT("\n文件将被复制到：\n") + DESTINATION_PATH;
    message += wxT("\n\n替换完成后将自动执行编译操作。");

    int result = wxMessageBox(message, wxT("确认替换"), wxYES_NO | wxICON_QUESTION, this);

    if (result == wxYES) {
        if (CopyFilesToDestination()) {
            wxMessageBox(wxT("文件替换成功！\n\n正在启动编译..."), wxT("成功"), wxOK | wxICON_INFORMATION, this);

            // Start compilation
            if (StartCompilation()) {
                EndModal(wxID_OK);
            } else {
                wxMessageBox(wxT("文件替换成功，但编译启动失败。\n请手动执行编译命令。"),
                            wxT("警告"), wxOK | wxICON_WARNING, this);
                EndModal(wxID_OK);
            }
        }
    }
}

void ReplaceAlgorithmDialog::OnCancel(wxCommandEvent& WXUNUSED(event))
{
    EndModal(wxID_CANCEL);
}

bool ReplaceAlgorithmDialog::ValidateFiles()
{
    // Check if at least one file/folder is selected
    if (m_mgsFlowPath.IsEmpty() && m_cImgDataBufferPath.IsEmpty() &&
        m_classificationProcessPath.IsEmpty() && m_generalProcessPath.IsEmpty() &&
        m_generalProcessHPath.IsEmpty() && m_processPath.IsEmpty() &&
        m_algorithmDataPath.IsEmpty()) {
        wxMessageBox(wxT("请至少选择一个文件或文件夹进行替换。"), wxT("错误"), wxOK | wxICON_ERROR, this);
        return false;
    }

    // Check if destination directory exists
    if (!wxFileName::DirExists(DESTINATION_PATH)) {
        wxMessageBox(wxString::Format(wxT("目标目录不存在：\n%s\n\n请确保Nuctech OSS系统已正确安装。"),
                    DESTINATION_PATH), wxT("错误"), wxOK | wxICON_ERROR, this);
        return false;
    }

    // Check if destination directory is writable
    if (!wxFileName::IsDirWritable(DESTINATION_PATH)) {
        wxMessageBox(wxString::Format(wxT("目标目录不可写：\n%s\n\n请检查目录权限。"),
                    DESTINATION_PATH), wxT("错误"), wxOK | wxICON_ERROR, this);
        return false;
    }

    // Validate each selected file
    wxArrayString filesToCheck;
    wxArrayString fileNames;

    if (!m_mgsFlowPath.IsEmpty()) {
        filesToCheck.Add(m_mgsFlowPath);
        fileNames.Add(wxT("mgs_flow.cpp"));
    }
    if (!m_cImgDataBufferPath.IsEmpty()) {
        filesToCheck.Add(m_cImgDataBufferPath);
        fileNames.Add(wxT("CImgDataBuffer.cpp"));
    }
    if (!m_classificationProcessPath.IsEmpty()) {
        filesToCheck.Add(m_classificationProcessPath);
        fileNames.Add(wxT("ClassificationProcess.cpp"));
    }
    if (!m_generalProcessPath.IsEmpty()) {
        filesToCheck.Add(m_generalProcessPath);
        fileNames.Add(wxT("GeneralProcess.cpp"));
    }
    if (!m_generalProcessHPath.IsEmpty()) {
        filesToCheck.Add(m_generalProcessHPath);
        fileNames.Add(wxT("GeneralProcess.h"));
    }
    if (!m_processPath.IsEmpty()) {
        filesToCheck.Add(m_processPath);
        fileNames.Add(wxT("process.cpp"));
    }

    for (size_t i = 0; i < filesToCheck.GetCount(); i++) {
        if (!wxFileName::FileExists(filesToCheck[i])) {
            wxMessageBox(wxString::Format(wxT("文件不存在：\n%s"), filesToCheck[i]),
                        wxT("错误"), wxOK | wxICON_ERROR, this);
            return false;
        }

        if (!wxFileName::IsFileReadable(filesToCheck[i])) {
            wxMessageBox(wxString::Format(wxT("文件不可读：\n%s"), filesToCheck[i]),
                        wxT("错误"), wxOK | wxICON_ERROR, this);
            return false;
        }
    }

    // Validate algorithm data folder if selected
    if (!m_algorithmDataPath.IsEmpty()) {
        if (!wxFileName::DirExists(m_algorithmDataPath)) {
            wxMessageBox(wxString::Format(wxT("算法数据文件夹不存在：\n%s"), m_algorithmDataPath),
                        wxT("错误"), wxOK | wxICON_ERROR, this);
            return false;
        }

        if (!wxFileName::IsDirReadable(m_algorithmDataPath)) {
            wxMessageBox(wxString::Format(wxT("算法数据文件夹不可读：\n%s"), m_algorithmDataPath),
                        wxT("错误"), wxOK | wxICON_ERROR, this);
            return false;
        }

        // Check if destination directory exists and is writable
        if (!wxFileName::DirExists(ALGORITHM_DATA_DESTINATION)) {
            wxMessageBox(wxString::Format(wxT("目标目录不存在：\n%s\n\n请确保Nuctech OSS系统已正确安装。"),
                        ALGORITHM_DATA_DESTINATION), wxT("错误"), wxOK | wxICON_ERROR, this);
            return false;
        }

        if (!wxFileName::IsDirWritable(ALGORITHM_DATA_DESTINATION)) {
            wxMessageBox(wxString::Format(wxT("目标目录不可写：\n%s\n\n请检查目录权限。"),
                        ALGORITHM_DATA_DESTINATION), wxT("错误"), wxOK | wxICON_ERROR, this);
            return false;
        }
    }

    return true;
}

bool ReplaceAlgorithmDialog::CopyFilesToDestination()
{
    wxProgressDialog progressDlg(wxT("复制文件"), wxT("正在复制文件..."), 100, this,
                                 wxPD_APP_MODAL | wxPD_AUTO_HIDE | wxPD_CAN_ABORT);

    int totalFiles = 0;
    int currentFile = 0;

    // Count total files to copy
    if (!m_mgsFlowPath.IsEmpty()) totalFiles++;
    if (!m_cImgDataBufferPath.IsEmpty()) totalFiles++;
    if (!m_classificationProcessPath.IsEmpty()) totalFiles++;
    if (!m_generalProcessPath.IsEmpty()) totalFiles++;
    if (!m_generalProcessHPath.IsEmpty()) totalFiles++;
    if (!m_processPath.IsEmpty()) totalFiles++;
    if (!m_algorithmDataPath.IsEmpty()) totalFiles++; // Count folder as one operation

    // Copy files
    if (!m_mgsFlowPath.IsEmpty()) {
        progressDlg.Update((currentFile * 100) / totalFiles, wxT("复制 mgs_flow.cpp..."));
        wxString destPath = DESTINATION_PATH + wxT("/mgs_flow.cpp");
        if (!wxCopyFile(m_mgsFlowPath, destPath, true)) {
            wxMessageBox(wxString::Format(wxT("复制文件失败：\n%s -> %s"), m_mgsFlowPath, destPath),
                        wxT("错误"), wxOK | wxICON_ERROR, this);
            return false;
        }
        currentFile++;
    }

    if (!m_cImgDataBufferPath.IsEmpty()) {
        progressDlg.Update((currentFile * 100) / totalFiles, wxT("复制 CImgDataBuffer.cpp..."));
        wxString destPath = DESTINATION_PATH + wxT("/CImgDataBuffer.cpp");
        if (!wxCopyFile(m_cImgDataBufferPath, destPath, true)) {
            wxMessageBox(wxString::Format(wxT("复制文件失败：\n%s -> %s"), m_cImgDataBufferPath, destPath),
                        wxT("错误"), wxOK | wxICON_ERROR, this);
            return false;
        }
        currentFile++;
    }

    if (!m_classificationProcessPath.IsEmpty()) {
        progressDlg.Update((currentFile * 100) / totalFiles, wxT("复制 ClassificationProcess.cpp..."));
        wxString destPath = DESTINATION_PATH + wxT("/ClassificationProcess.cpp");
        if (!wxCopyFile(m_classificationProcessPath, destPath, true)) {
            wxMessageBox(wxString::Format(wxT("复制文件失败：\n%s -> %s"), m_classificationProcessPath, destPath),
                        wxT("错误"), wxOK | wxICON_ERROR, this);
            return false;
        }
        currentFile++;
    }

    if (!m_generalProcessPath.IsEmpty()) {
        progressDlg.Update((currentFile * 100) / totalFiles, wxT("复制 GeneralProcess.cpp..."));
        wxString destPath = DESTINATION_PATH + wxT("/GeneralProcess.cpp");
        if (!wxCopyFile(m_generalProcessPath, destPath, true)) {
            wxMessageBox(wxString::Format(wxT("复制文件失败：\n%s -> %s"), m_generalProcessPath, destPath),
                        wxT("错误"), wxOK | wxICON_ERROR, this);
            return false;
        }
        currentFile++;
    }

    if (!m_generalProcessHPath.IsEmpty()) {
        progressDlg.Update((currentFile * 100) / totalFiles, wxT("复制 GeneralProcess.h..."));
        wxString destPath = DESTINATION_PATH + wxT("/GeneralProcess.h");
        if (!wxCopyFile(m_generalProcessHPath, destPath, true)) {
            wxMessageBox(wxString::Format(wxT("复制文件失败：\n%s -> %s"), m_generalProcessHPath, destPath),
                        wxT("错误"), wxOK | wxICON_ERROR, this);
            return false;
        }
        currentFile++;
    }

    if (!m_processPath.IsEmpty()) {
        progressDlg.Update((currentFile * 100) / totalFiles, wxT("复制 process.cpp..."));
        wxString destPath = DESTINATION_PATH + wxT("/process.cpp");
        if (!wxCopyFile(m_processPath, destPath, true)) {
            wxMessageBox(wxString::Format(wxT("复制文件失败：\n%s -> %s"), m_processPath, destPath),
                        wxT("错误"), wxOK | wxICON_ERROR, this);
            return false;
        }
        currentFile++;
    }

    if (!m_algorithmDataPath.IsEmpty()) {
        progressDlg.Update((currentFile * 100) / totalFiles, wxT("复制算法数据文件夹..."));

        // Create destination path for AlgorithmData
        wxString destPath = ALGORITHM_DATA_DESTINATION + wxT("/AlgorithmData");

        // Remove existing AlgorithmData folder if it exists
        if (wxFileName::DirExists(destPath)) {
            if (!wxFileName::Rmdir(destPath, wxPATH_RMDIR_RECURSIVE)) {
                wxMessageBox(wxString::Format(wxT("删除现有目录失败：\n%s"), destPath),
                            wxT("错误"), wxOK | wxICON_ERROR, this);
                return false;
            }
        }

        // Copy the entire folder and rename it to AlgorithmData
        if (!CopyDirectory(m_algorithmDataPath, destPath)) {
            wxMessageBox(wxString::Format(wxT("复制文件夹失败：\n%s -> %s"), m_algorithmDataPath, destPath),
                        wxT("错误"), wxOK | wxICON_ERROR, this);
            return false;
        }
        currentFile++;
    }

    progressDlg.Update(100, wxT("文件复制完成"));
    return true;
}

bool ReplaceAlgorithmDialog::StartCompilation()
{
    // 检测是否在AppImage环境中运行
    wxString appImagePath, appDir;
    bool isAppImage = wxGetEnv(wxT("APPIMAGE"), &appImagePath) || wxGetEnv(wxT("APPDIR"), &appDir);

    wxString workingDir = wxT("/home/<USER>/Nuctech_Services/OSS_MR");
    wxString scriptPath;
    wxString scriptArgs = wxT("mgspkg -r 1");
    
    // 查找系统中的脚本
    wxArrayString possibleScriptPaths;
    possibleScriptPaths.Add(workingDir + wxT("/single_pkg_build.sh"));
    possibleScriptPaths.Add(wxT("/usr/bin/single_pkg_build.sh"));
    possibleScriptPaths.Add(wxT("/usr/local/bin/single_pkg_build.sh"));
    
    if (isAppImage) {
        if (wxGetEnv(wxT("APPDIR"), &appDir)) {
            possibleScriptPaths.Add(appDir + wxT("/usr/bin/single_pkg_build.sh"));
        }
        if (wxGetEnv(wxT("APPIMAGE"), &appImagePath)) {
            wxFileName appImageFile(appImagePath);
            wxString appImageDir = appImageFile.GetPath();
            possibleScriptPaths.Add(appImageDir + wxT("/usr/bin/single_pkg_build.sh"));
        }
    }
    
    // 查找第一个存在的脚本
    scriptPath = wxEmptyString;
    for (size_t i = 0; i < possibleScriptPaths.GetCount(); i++) {
        if (wxFileName::FileExists(possibleScriptPaths[i])) {
            scriptPath = possibleScriptPaths[i];
            break;
        }
    }
    
    if (scriptPath.IsEmpty()) {
        wxMessageBox(wxT("无法找到编译脚本。请确保single_pkg_build.sh存在于系统中。"),
                    wxT("错误"), wxOK | wxICON_ERROR, this);
        return false;
    }
    
    // 显示编译开始消息
    wxMessageBox(wxT("正在启动编译，请稍候..."), wxT("编译中"), wxOK | wxICON_INFORMATION, this);

    // 设置脚本执行权限
    wxString chmodCommand = wxString::Format(wxT("chmod +x \"%s\""), scriptPath);
    wxExecute(chmodCommand, wxEXEC_SYNC);

    // 显示进度对话框
    wxProgressDialog progressDlg(wxT("编译进度"),
                                 wxT("正在启动编译，请稍候..."),
                                 100, this,
                                 wxPD_APP_MODAL | wxPD_CAN_ABORT | wxPD_ELAPSED_TIME);

    // 平滑的进度更新
    for (int i = 0; i <= 10; i++) {
        progressDlg.Update(i, wxT("正在检查编译环境..."));
        wxMilliSleep(50);
        wxYield(); // 让界面响应
    }

    // 构建编译命令，先加载环境变量
    wxString setupScript = wxT("/home/<USER>/Nuctech_Services/OSS_MR/Release/setup.bash");
    wxString bashCommand;

    if (isAppImage) {
        // AppImage环境：先加载环境变量，然后切换到工作目录，执行打包的脚本
        bashCommand = wxString::Format(
            wxT("bash -c \"source %s && cd %s && bash '%s' %s\""),
            setupScript, workingDir, scriptPath, scriptArgs);
    } else {
        // 本地环境：先加载环境变量，然后执行脚本
        bashCommand = wxString::Format(
            wxT("bash -c \"source %s && cd %s && bash %s %s\""),
            setupScript, workingDir, scriptPath, scriptArgs);
    }

    progressDlg.Update(20, wxT("正在执行编译命令..."));

    // 执行编译命令
    wxArrayString output, errors;
    int exitCode = wxExecute(bashCommand, output, errors, wxEXEC_SYNC);

    progressDlg.Update(100, wxT("编译完成"));

    // 显示编译结果
    if (exitCode == 0) {
        // 编译成功，删除已替换的文件
        // CleanupReplacedFiles();
        wxMessageBox(wxT("编译成功完成！\n\n已自动清理替换的文件。"), wxT("编译结果"), wxOK | wxICON_INFORMATION, this);
        return true;
    } else {
        wxString errorMsg = wxString::Format(wxT("编译失败，退出代码: %d\n\n"), exitCode);
        if (!errors.IsEmpty()) {
            errorMsg += wxT("错误信息:\n");
            for (size_t i = 0; i < errors.GetCount() && i < 10; i++) {
                errorMsg += errors[i] + wxT("\n");
            }
        }
        wxMessageBox(errorMsg, wxT("编译失败,请确认文件是否正确"), wxOK | wxICON_ERROR, this);
        return false;
    }
    
    return true;
}

void ReplaceAlgorithmDialog::CleanupReplacedFiles()
{
    wxLogMessage(wxT("开始清理替换的文件..."));

    // 删除已复制到目标目录的文件
    wxArrayString filesToDelete;

    if (!m_mgsFlowPath.IsEmpty()) {
        filesToDelete.Add(DESTINATION_PATH + wxT("/mgs_flow.cpp"));
    }

    if (!m_cImgDataBufferPath.IsEmpty()) {
        filesToDelete.Add(DESTINATION_PATH + wxT("/CImgDataBuffer.cpp"));
    }

    if (!m_classificationProcessPath.IsEmpty()) {
        filesToDelete.Add(DESTINATION_PATH + wxT("/ClassificationProcess.cpp"));
    }

    if (!m_generalProcessPath.IsEmpty()) {
        filesToDelete.Add(DESTINATION_PATH + wxT("/GeneralProcess.cpp"));
    }

    // if (!m_generalProcessHPath.IsEmpty()) {
    //     filesToDelete.Add(DESTINATION_PATH + wxT("/GeneralProcess.h"));
    // }

    if (!m_processPath.IsEmpty()) {
        filesToDelete.Add(DESTINATION_PATH + wxT("/process.cpp"));
    }

    // 执行删除操作
    for (size_t i = 0; i < filesToDelete.GetCount(); i++) {
        const wxString& filePath = filesToDelete[i];
        if (wxFileExists(filePath)) {
            if (wxRemoveFile(filePath)) {
                wxLogMessage(wxT("已删除文件: %s"), filePath);
            } else {
                wxLogMessage(wxT("删除文件失败: %s"), filePath);
            }
        }
    }

    wxLogMessage(wxT("文件清理完成"));
}

bool ReplaceAlgorithmDialog::CopyDirectory(const wxString& source, const wxString& destination)
{
    // Create destination directory
    if (!wxFileName::Mkdir(destination, wxS_DIR_DEFAULT, wxPATH_MKDIR_FULL)) {
        return false;
    }

    wxDir sourceDir(source);
    if (!sourceDir.IsOpened()) {
        return false;
    }

    wxString filename;
    bool cont = sourceDir.GetFirst(&filename);

    while (cont) {
        wxString sourcePath = source + wxFileName::GetPathSeparator() + filename;
        wxString destPath = destination + wxFileName::GetPathSeparator() + filename;

        if (wxFileName::DirExists(sourcePath)) {
            // Recursively copy subdirectory
            if (!CopyDirectory(sourcePath, destPath)) {
                return false;
            }
        } else {
            // Copy file
            if (!wxCopyFile(sourcePath, destPath, true)) {
                return false;
            }
        }

        cont = sourceDir.GetNext(&filename);
    }

    return true;
}
void ReplaceAlgorithmDialog::OnOptionChanged(wxCommandEvent& event)
{
    // 互斥逻辑：只能勾选一个
    if (event.GetEventObject() == m_debugDiskCheckBox && m_debugDiskCheckBox->IsChecked()) {
        m_algorithmRecompileCheckBox->SetValue(false);
    } else if (event.GetEventObject() == m_algorithmRecompileCheckBox && m_algorithmRecompileCheckBox->IsChecked()) {
        m_debugDiskCheckBox->SetValue(false);
    }
    UpdateFilePickersEnable();
}

void ReplaceAlgorithmDialog::UpdateFilePickersEnable()
{
    bool debugChecked = m_debugDiskCheckBox->IsChecked();
    bool algoChecked = m_algorithmRecompileCheckBox->IsChecked();
    bool enablePickers = debugChecked || algoChecked;
    // 先全部禁用
    m_mgsFlowPicker->Enable(enablePickers && !debugChecked);
    m_cImgDataBufferPicker->Enable(enablePickers && !debugChecked);
    m_classificationProcessPicker->Enable(enablePickers && !debugChecked);
    m_processPicker->Enable(enablePickers && !algoChecked);
    m_algorithmDataPicker->Enable(enablePickers && !algoChecked);
    m_generalProcessPicker->Enable(enablePickers);
    m_generalProcessHPicker->Enable(enablePickers);
}
