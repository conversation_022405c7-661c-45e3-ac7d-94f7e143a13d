exe_path=$(cd `dirname $0`;pwd)
src_path=$exe_path/src
debug_setup=$exe_path/Debug/setup.bash
release_setup=$exe_path/Release/setup.bash

if [ ! -e $src_path/CMakeLists.txt ]; then
  echo -e "\033[33m ** The shell script must be under the root path of oss project! ** \033[0m"
  exit
fi

if [ -z "$1" -o -z "$2" ]; then
 echo -e "\033[33m ** USAGE: build_oss_single_pkg.sh <pkg_name> <-r|-d> [version] **\033[0m"
 exit
fi

pack_dir=$(rospack find $1 2>&1)
if [[ ${pack_dir} =~ .*Error:.*not\ found$ ]]; then
 echo -e "\033[31m **the specified pkg does not exist **\033[0m"
 exit
fi

b_version=1
declare -a no_version_pkges=(io_data_process \
                    peripheral_device_ftp peripheral_device_tcp scs_client \
                    oss_launcher robot_upstart \
                    )

for pkg in ${no_version_pkges[*]}  
do
    if [ $pkg == $1 ]; then
        b_version=0
        echo -e " **  build no version pkg $pkg   **"
    fi
done

if [ $b_version == 1 ]; then
    if [ -z "$3" ]; then
        version_file=$pack_dir/include/VersionConfig.h
        if [ ! -f $version_file ]; then
            echo -e "\033[31m **$version_file does not exist **\033[0m"
            exit
        fi
        cur_version=$(sed -n '/#define\ VERSION_OSS/p' $version_file | awk '{print $3}' | awk -F "\"" '{print $2}')
        echo $cur_version
    else
        cur_version=$3
    fi

    version_fields=$(echo "$cur_version" | awk -F "." '{print NF}')
    if [ $version_fields -ne 4 -o -ne 1 ]; then
        echo -e "\033[31m ** Version No maybe incorrect, it should be something like MAJOR.MINOR.PATCH.REVISION **\033[0m"
        echo -e "\033[31m ** or shared library should be specified one bit **\033[0m"
        exit
    fi
fi


if [ "$2" != "-r" -a "$2" != "-d" ]; then
 echo -e "\033[31m ** Build type must be -r or-d **\033[0m"
 exit
fi


if [ $2 = "-d" ]; then
	build_params="-DCATKIN_DEVEL_PREFIX=$exe_path/Debug -DCMAKE_BUILD_TYPE=Debug -DOSS_VERSION_$1=$cur_version"
    build_type="Debug"
	backup_type="Release"
else 
	build_params="-DCATKIN_DEVEL_PREFIX=$exe_path/Release -DCMAKE_BUILD_TYPE=Release -DOSS_VERSION_$1=$cur_version"
    build_type="Release"
	backup_type="Debug"
fi

build_dir=$exe_path/build/$build_type

sed -i '/export OSS_LATEST_BUILD_TYPE/d' ~/.bashrc
echo "export OSS_LATEST_BUILD_TYPE=$build_type" >>~/.bashrc

#rm $exe_path/$build_type/lib/$1 -r -f
#rm $exe_path/$build_type/lib/liboss_version.a -f
#rm $exe_path/build/$build_type/$1 -r -f
#due to overlay
#mv $exe_path/$backup_type $exe_path/${backup_type}_bk -f
#echo -e "\033[33m ----------------------------------- building oss_version ----------------------------------- \033[0m"
#catkin_make -DCATKIN_WHITELIST_PACKAGES="oss_version" $build_params --source $src_path --build $build_dir --force-cmake --make-args -B
#source $exe_path/$build_type/setup.bash
#echo -e "\033[33m ----------------------------------- building io_data_process;cmake_modules ----------------- \033[0m"
#catkin_make -DCATKIN_WHITELIST_PACKAGES="io_data_process;cmake_modules" $build_params --source $src_path --build $build_dir
#echo -e "\033[33m ----------------------------------- building common_modules -------------------------------- \033[0m"
#catkin_make -DCATKIN_WHITELIST_PACKAGES="common_modules" $build_params --source $src_path --build $build_dir
echo -e "\033[33m ----------------------------------- building Package ---------------------------------- \033[0m"
#-DCMAKE_CXX_FLAGS="-w"
#-DCATKIN_BLACKLIST_PACKAGES="common_modules"
catkin_make -DCATKIN_WHITELIST_PACKAGES="$1" $build_params --source $src_path --build $build_dir --make-args -B

error_code=$?
if [ $error_code -eq 1 ]; then
    echo -e "\033[33m ----------------------------------- building failed ---------------------------------------- \033[0m"
fi
exit $error_code


