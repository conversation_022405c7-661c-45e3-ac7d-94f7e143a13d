#!/usr/bin/env python3

# MCP File Server - AppImage兼容包装器
# 这个脚本确保在AppImage环境中能正确找到并运行MCP服务器

import sys
import os
import asyncio

# 获取脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))

# 定义可能的Python文件位置
possible_paths = [
    script_dir,  # /usr/bin/
    os.path.join(script_dir, '..', 'share', 'mini_tool'),  # /usr/share/mini_tool/
]

# 添加路径到sys.path
for path in possible_paths:
    if os.path.exists(path) and path not in sys.path:
        sys.path.insert(0, path)

# 查找实际的mcp_file_server.py文件
actual_script = None
for path in possible_paths:
    candidate = os.path.join(path, 'mcp_file_server.py')
    if os.path.exists(candidate) and candidate != __file__:
        actual_script = candidate
        break

if actual_script:
    # 直接执行找到的脚本文件
    try:
        with open(actual_script, 'r', encoding='utf-8') as f:
            code = f.read()

        # 设置__file__变量为实际脚本路径
        globals_dict = {
            '__file__': actual_script,
            '__name__': '__main__'
        }

        exec(code, globals_dict)
    except Exception as e:
        print(f"执行MCP服务器时出错: {e}", file=sys.stderr)
        sys.exit(1)
else:
    print("错误: 无法找到mcp_file_server.py文件", file=sys.stderr)
    print(f"搜索路径: {possible_paths}", file=sys.stderr)
    sys.exit(1)
