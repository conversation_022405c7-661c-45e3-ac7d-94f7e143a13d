# Mini Tool v1.0.0

## 简介
Mini Tool 是一个基于 wxWidgets 的系统管理工具集合。

## 功能特性
- 系统信息查询：显示详细的硬件和系统信息
- 删除源码：安全备份并删除指定目录中的 .cpp 文件
- 直观的图形界面：4x4 按钮网格布局

## 系统要求
- Linux 操作系统
- wxWidgets 3.0 或更高版本
- 基本的系统工具（tar, find, lscpu 等）

## 安装和运行

### 方法1：使用启动脚本（推荐）
```bash
chmod +x start.sh
./start.sh
```

### 方法2：直接运行
```bash
chmod +x mini_tool
./mini_tool
```

## 注意事项
1. 删除源码功能会备份文件到 /tmp 目录
2. 系统信息查询需要相应的系统权限
3. 某些功能可能需要管理员权限才能获取完整信息

## 故障排除

### 依赖问题
如果程序无法启动，请检查是否安装了 wxWidgets：
```bash
# Ubuntu/Debian
sudo apt-get install libwxgtk3.0-gtk3-dev

# CentOS/RHEL
sudo yum install wxGTK3-devel
```

### 权限问题
确保程序有执行权限：
```bash
chmod +x mini_tool
```

## 版本信息
- 版本: 1.0.0
- 构建日期: 2025-06-20
- 作者: LSQ

