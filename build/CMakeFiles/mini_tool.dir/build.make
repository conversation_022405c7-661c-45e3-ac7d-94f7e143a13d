# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/Downloads/cmake-3.26.3-linux-x86_64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/Downloads/cmake-3.26.3-linux-x86_64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/lsq/mini_tool

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/lsq/mini_tool/build

# Include any dependencies generated for this target.
include CMakeFiles/mini_tool.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/mini_tool.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/mini_tool.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/mini_tool.dir/flags.make

CMakeFiles/mini_tool.dir/src/main.cpp.o: CMakeFiles/mini_tool.dir/flags.make
CMakeFiles/mini_tool.dir/src/main.cpp.o: /home/<USER>/lsq/mini_tool/src/main.cpp
CMakeFiles/mini_tool.dir/src/main.cpp.o: CMakeFiles/mini_tool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lsq/mini_tool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/mini_tool.dir/src/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mini_tool.dir/src/main.cpp.o -MF CMakeFiles/mini_tool.dir/src/main.cpp.o.d -o CMakeFiles/mini_tool.dir/src/main.cpp.o -c /home/<USER>/lsq/mini_tool/src/main.cpp

CMakeFiles/mini_tool.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mini_tool.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lsq/mini_tool/src/main.cpp > CMakeFiles/mini_tool.dir/src/main.cpp.i

CMakeFiles/mini_tool.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mini_tool.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lsq/mini_tool/src/main.cpp -o CMakeFiles/mini_tool.dir/src/main.cpp.s

CMakeFiles/mini_tool.dir/src/MainFrame.cpp.o: CMakeFiles/mini_tool.dir/flags.make
CMakeFiles/mini_tool.dir/src/MainFrame.cpp.o: /home/<USER>/lsq/mini_tool/src/MainFrame.cpp
CMakeFiles/mini_tool.dir/src/MainFrame.cpp.o: CMakeFiles/mini_tool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lsq/mini_tool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/mini_tool.dir/src/MainFrame.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mini_tool.dir/src/MainFrame.cpp.o -MF CMakeFiles/mini_tool.dir/src/MainFrame.cpp.o.d -o CMakeFiles/mini_tool.dir/src/MainFrame.cpp.o -c /home/<USER>/lsq/mini_tool/src/MainFrame.cpp

CMakeFiles/mini_tool.dir/src/MainFrame.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mini_tool.dir/src/MainFrame.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lsq/mini_tool/src/MainFrame.cpp > CMakeFiles/mini_tool.dir/src/MainFrame.cpp.i

CMakeFiles/mini_tool.dir/src/MainFrame.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mini_tool.dir/src/MainFrame.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lsq/mini_tool/src/MainFrame.cpp -o CMakeFiles/mini_tool.dir/src/MainFrame.cpp.s

CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.o: CMakeFiles/mini_tool.dir/flags.make
CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.o: /home/<USER>/lsq/mini_tool/src/LoginDialog.cpp
CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.o: CMakeFiles/mini_tool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lsq/mini_tool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.o -MF CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.o.d -o CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.o -c /home/<USER>/lsq/mini_tool/src/LoginDialog.cpp

CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lsq/mini_tool/src/LoginDialog.cpp > CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.i

CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lsq/mini_tool/src/LoginDialog.cpp -o CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.s

CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.o: CMakeFiles/mini_tool.dir/flags.make
CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.o: /home/<USER>/lsq/mini_tool/src/DeleteSourceDialog.cpp
CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.o: CMakeFiles/mini_tool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lsq/mini_tool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.o -MF CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.o.d -o CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.o -c /home/<USER>/lsq/mini_tool/src/DeleteSourceDialog.cpp

CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lsq/mini_tool/src/DeleteSourceDialog.cpp > CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.i

CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lsq/mini_tool/src/DeleteSourceDialog.cpp -o CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.s

CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.o: CMakeFiles/mini_tool.dir/flags.make
CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.o: /home/<USER>/lsq/mini_tool/src/SystemInfoDialog.cpp
CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.o: CMakeFiles/mini_tool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lsq/mini_tool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.o -MF CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.o.d -o CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.o -c /home/<USER>/lsq/mini_tool/src/SystemInfoDialog.cpp

CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lsq/mini_tool/src/SystemInfoDialog.cpp > CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.i

CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lsq/mini_tool/src/SystemInfoDialog.cpp -o CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.s

CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.o: CMakeFiles/mini_tool.dir/flags.make
CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.o: /home/<USER>/lsq/mini_tool/src/UdpTestDialog.cpp
CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.o: CMakeFiles/mini_tool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lsq/mini_tool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.o -MF CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.o.d -o CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.o -c /home/<USER>/lsq/mini_tool/src/UdpTestDialog.cpp

CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lsq/mini_tool/src/UdpTestDialog.cpp > CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.i

CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lsq/mini_tool/src/UdpTestDialog.cpp -o CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.s

CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.o: CMakeFiles/mini_tool.dir/flags.make
CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.o: /home/<USER>/lsq/mini_tool/src/ChangeConfigDialog.cpp
CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.o: CMakeFiles/mini_tool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lsq/mini_tool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.o -MF CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.o.d -o CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.o -c /home/<USER>/lsq/mini_tool/src/ChangeConfigDialog.cpp

CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lsq/mini_tool/src/ChangeConfigDialog.cpp > CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.i

CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lsq/mini_tool/src/ChangeConfigDialog.cpp -o CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.s

CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.o: CMakeFiles/mini_tool.dir/flags.make
CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.o: /home/<USER>/lsq/mini_tool/src/ReplaceAlgorithmDialog.cpp
CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.o: CMakeFiles/mini_tool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lsq/mini_tool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.o -MF CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.o.d -o CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.o -c /home/<USER>/lsq/mini_tool/src/ReplaceAlgorithmDialog.cpp

CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lsq/mini_tool/src/ReplaceAlgorithmDialog.cpp > CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.i

CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lsq/mini_tool/src/ReplaceAlgorithmDialog.cpp -o CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.s

CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.o: CMakeFiles/mini_tool.dir/flags.make
CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.o: /home/<USER>/lsq/mini_tool/src/ProgressDialog.cpp
CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.o: CMakeFiles/mini_tool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lsq/mini_tool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.o -MF CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.o.d -o CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.o -c /home/<USER>/lsq/mini_tool/src/ProgressDialog.cpp

CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lsq/mini_tool/src/ProgressDialog.cpp > CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.i

CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lsq/mini_tool/src/ProgressDialog.cpp -o CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.s

CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.o: CMakeFiles/mini_tool.dir/flags.make
CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.o: /home/<USER>/lsq/mini_tool/src/AiChatDialog.cpp
CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.o: CMakeFiles/mini_tool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lsq/mini_tool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.o -MF CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.o.d -o CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.o -c /home/<USER>/lsq/mini_tool/src/AiChatDialog.cpp

CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lsq/mini_tool/src/AiChatDialog.cpp > CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.i

CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lsq/mini_tool/src/AiChatDialog.cpp -o CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.s

CMakeFiles/mini_tool.dir/src/MCPClient.cpp.o: CMakeFiles/mini_tool.dir/flags.make
CMakeFiles/mini_tool.dir/src/MCPClient.cpp.o: /home/<USER>/lsq/mini_tool/src/MCPClient.cpp
CMakeFiles/mini_tool.dir/src/MCPClient.cpp.o: CMakeFiles/mini_tool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lsq/mini_tool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/mini_tool.dir/src/MCPClient.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mini_tool.dir/src/MCPClient.cpp.o -MF CMakeFiles/mini_tool.dir/src/MCPClient.cpp.o.d -o CMakeFiles/mini_tool.dir/src/MCPClient.cpp.o -c /home/<USER>/lsq/mini_tool/src/MCPClient.cpp

CMakeFiles/mini_tool.dir/src/MCPClient.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mini_tool.dir/src/MCPClient.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lsq/mini_tool/src/MCPClient.cpp > CMakeFiles/mini_tool.dir/src/MCPClient.cpp.i

CMakeFiles/mini_tool.dir/src/MCPClient.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mini_tool.dir/src/MCPClient.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lsq/mini_tool/src/MCPClient.cpp -o CMakeFiles/mini_tool.dir/src/MCPClient.cpp.s

# Object files for target mini_tool
mini_tool_OBJECTS = \
"CMakeFiles/mini_tool.dir/src/main.cpp.o" \
"CMakeFiles/mini_tool.dir/src/MainFrame.cpp.o" \
"CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.o" \
"CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.o" \
"CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.o" \
"CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.o" \
"CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.o" \
"CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.o" \
"CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.o" \
"CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.o" \
"CMakeFiles/mini_tool.dir/src/MCPClient.cpp.o"

# External object files for target mini_tool
mini_tool_EXTERNAL_OBJECTS =

mini_tool: CMakeFiles/mini_tool.dir/src/main.cpp.o
mini_tool: CMakeFiles/mini_tool.dir/src/MainFrame.cpp.o
mini_tool: CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.o
mini_tool: CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.o
mini_tool: CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.o
mini_tool: CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.o
mini_tool: CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.o
mini_tool: CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.o
mini_tool: CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.o
mini_tool: CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.o
mini_tool: CMakeFiles/mini_tool.dir/src/MCPClient.cpp.o
mini_tool: CMakeFiles/mini_tool.dir/build.make
mini_tool: CMakeFiles/mini_tool.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/lsq/mini_tool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Linking CXX executable mini_tool"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/mini_tool.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/mini_tool.dir/build: mini_tool
.PHONY : CMakeFiles/mini_tool.dir/build

CMakeFiles/mini_tool.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/mini_tool.dir/cmake_clean.cmake
.PHONY : CMakeFiles/mini_tool.dir/clean

CMakeFiles/mini_tool.dir/depend:
	cd /home/<USER>/lsq/mini_tool/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/lsq/mini_tool /home/<USER>/lsq/mini_tool /home/<USER>/lsq/mini_tool/build /home/<USER>/lsq/mini_tool/build /home/<USER>/lsq/mini_tool/build/CMakeFiles/mini_tool.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/mini_tool.dir/depend

