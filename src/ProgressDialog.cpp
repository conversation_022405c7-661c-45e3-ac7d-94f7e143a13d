#include "ProgressDialog.h"
#include <wx/msgdlg.h>
#include <wx/process.h>
#include <wx/stream.h>
#include <wx/txtstrm.h>
#include <wx/filename.h>

wxBEGIN_EVENT_TABLE(ProgressDialog, wxDialog)
    EVT_BUTTON(wxID_CANCEL, ProgressDialog::OnCancel)
    EVT_CLOSE(ProgressDialog::OnClose)
    EVT_TIMER(wxID_ANY, ProgressDialog::OnTimer)
wxEND_EVENT_TABLE()

ProgressDialog::ProgressDialog(wxWindow* parent, const wxString& title, const wxString& message)
    : wxDialog(parent, wxID_ANY, title, wxDefaultPosition, wxSize(400, 200), 
               wxDEFAULT_DIALOG_STYLE & ~wxCLOSE_BOX)
    , m_success(false)
    , m_cancelled(false)
    , m_timer(nullptr)
    , m_currentProgress(0)
    , m_completed(false)
{
    // Create main sizer
    wxBoxSizer* mainSizer = new wxBoxSizer(wxVERTICAL);
    
    // Message text
    m_messageText = new wxStaticText(this, wxID_ANY, message);
    mainSizer->Add(m_messageText, 0, wxALL | wxEXPAND, 10);
    
    // Progress bar
    m_progressBar = new wxGauge(this, wxID_ANY, 100);
    mainSizer->Add(m_progressBar, 0, wxALL | wxEXPAND, 10);
    
    // Status text
    m_statusText = new wxStaticText(this, wxID_ANY, wxT("准备中..."));
    mainSizer->Add(m_statusText, 0, wxALL | wxEXPAND, 10);
    
    // Cancel button
    m_cancelButton = new wxButton(this, wxID_CANCEL, wxT("取消"));
    mainSizer->Add(m_cancelButton, 0, wxALL | wxCENTER, 10);
    
    SetSizer(mainSizer);
    Centre();
    
    // Create timer for progress updates
    m_timer = new wxTimer(this);
}

ProgressDialog::~ProgressDialog()
{
    if (m_timer) {
        m_timer->Stop();
        delete m_timer;
    }
}

bool ProgressDialog::StartCompression(const wxString& sourceDir, const wxString& targetFile)
{
    // Start the compression thread
    CompressionThread* thread = new CompressionThread(this, sourceDir, targetFile);
    
    if (thread->Create() != wxTHREAD_NO_ERROR) {
        delete thread;
        m_errorMessage = wxT("无法创建压缩线程");
        return false;
    }
    
    if (thread->Run() != wxTHREAD_NO_ERROR) {
        delete thread;
        m_errorMessage = wxT("无法启动压缩线程");
        return false;
    }
    
    // Start timer for progress updates
    m_timer->Start(100); // Update every 100ms
    
    return true;
}

void ProgressDialog::OnCancel(wxCommandEvent& WXUNUSED(event))
{
    m_cancelled = true;
    m_statusText->SetLabel(wxT("正在取消..."));
    m_cancelButton->Enable(false);
}

void ProgressDialog::OnClose(wxCloseEvent& event)
{
    if (m_completed) {
        event.Skip();
    } else {
        // Don't allow closing while in progress
        event.Veto();
    }
}

void ProgressDialog::OnTimer(wxTimerEvent& WXUNUSED(event))
{
    wxCriticalSectionLocker lock(m_progressCS);
    
    if (m_completed) {
        m_timer->Stop();
        m_cancelButton->SetLabel(wxT("关闭"));
        m_cancelButton->Enable(true);
        
        if (m_success) {
            m_statusText->SetLabel(wxT("压缩完成！"));
            m_progressBar->SetValue(100);
        } else {
            m_statusText->SetLabel(wxT("压缩失败：") + m_errorMessage);
        }
        return;
    }
    
    // Update progress
    m_progressBar->SetValue(m_currentProgress);
    m_statusText->SetLabel(m_currentStatus);
}

void ProgressDialog::UpdateProgress(int percentage, const wxString& status)
{
    wxCriticalSectionLocker lock(m_progressCS);
    m_currentProgress = percentage;
    m_currentStatus = status;
}

void ProgressDialog::SetCompleted(bool success, const wxString& message)
{
    wxCriticalSectionLocker lock(m_progressCS);
    m_completed = true;
    m_success = success;
    if (!message.IsEmpty()) {
        m_errorMessage = message;
    }
}

// CompressionThread implementation
CompressionThread::CompressionThread(ProgressDialog* dialog, const wxString& sourceDir, const wxString& targetFile)
    : wxThread(wxTHREAD_DETACHED)
    , m_dialog(dialog)
    , m_sourceDir(sourceDir)
    , m_targetFile(targetFile)
{
}

wxThread::ExitCode CompressionThread::Entry()
{
    bool success = CompressDirectory();
    SetCompleted(success);
    return (wxThread::ExitCode)0;
}

bool CompressionThread::CompressDirectory()
{
    UpdateProgress(10, wxT("开始压缩..."));

    // Use tar command to compress the directory
    wxString command = wxString::Format(
        wxT("tar -czf \"%s\" -C \"%s\" ."),
        m_targetFile,
        m_sourceDir
    );

    UpdateProgress(20, wxT("执行压缩命令..."));

    // Execute the command
    wxProcess process;
    long result = wxExecute(command, wxEXEC_SYNC);

    if (result == 0) {
        UpdateProgress(90, wxT("验证压缩文件..."));

        // Check if the file was created and has reasonable size
        if (wxFileName::FileExists(m_targetFile)) {
            wxFileName file(m_targetFile);
            if (file.GetSize() > 0) {
                UpdateProgress(100, wxT("压缩完成"));
                return true;
            } else {
                SetCompleted(false, wxT("压缩文件大小为0"));
                return false;
            }
        } else {
            SetCompleted(false, wxT("压缩文件未创建"));
            return false;
        }
    } else {
        SetCompleted(false, wxString::Format(wxT("压缩命令执行失败，返回码：%ld"), result));
        return false;
    }
}

void CompressionThread::UpdateProgress(int percentage, const wxString& status)
{
    if (m_dialog) {
        m_dialog->UpdateProgress(percentage, status);
    }
}

void CompressionThread::SetCompleted(bool success, const wxString& message)
{
    if (m_dialog) {
        m_dialog->SetCompleted(success, message);
    }
}
