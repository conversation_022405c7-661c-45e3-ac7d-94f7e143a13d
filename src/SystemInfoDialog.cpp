#include "SystemInfoDialog.h"
#include <wx/msgdlg.h>
#include <wx/filedlg.h>
#include <wx/process.h>
#include <wx/stream.h>
#include <wx/txtstrm.h>
#include <wx/datetime.h>
#include <wx/filename.h>
#include <wx/stdpaths.h>
#include <fstream>
#include <cstdio>

wxBEGIN_EVENT_TABLE(SystemInfoDialog, wxDialog)
    EVT_BUTTON(wxID_REFRESH, SystemInfoDialog::OnRefresh)
    EVT_BUTTON(wxID_SAVE, SystemInfoDialog::OnSave)
    EVT_BUTTON(wxID_CLOSE, SystemInfoDialog::OnClose)
wxEND_EVENT_TABLE()

SystemInfoDialog::SystemInfoDialog(wxWindow* parent)
    : wxDialog(parent, wxID_ANY, wxT("系统信息"), wxDefaultPosition, wxSize(800, 600))
{
    // Create main sizer
    wxBoxSizer* mainSizer = new wxBoxSizer(wxVERTICAL);
    
    // Title
    wxStaticText* titleText = new wxStaticText(this, wxID_ANY, wxT("服务器硬件信息报告"));
    wxFont titleFont = titleText->GetFont();
    titleFont.SetPointSize(14);
    titleFont.SetWeight(wxFONTWEIGHT_BOLD);
    titleText->SetFont(titleFont);
    mainSizer->Add(titleText, 0, wxALL | wxCENTER, 10);
    
    // Info text control
    m_infoText = new wxTextCtrl(this, wxID_ANY, wxEmptyString, 
        wxDefaultPosition, wxDefaultSize, 
        wxTE_MULTILINE | wxTE_READONLY | wxTE_WORDWRAP);
    
    // Set monospace font for better formatting
    wxFont monoFont(10, wxFONTFAMILY_TELETYPE, wxFONTSTYLE_NORMAL, wxFONTWEIGHT_NORMAL);
    m_infoText->SetFont(monoFont);
    
    mainSizer->Add(m_infoText, 1, wxALL | wxEXPAND, 10);
    
    // Button sizer
    wxBoxSizer* buttonSizer = new wxBoxSizer(wxHORIZONTAL);
    
    m_refreshButton = new wxButton(this, wxID_REFRESH, wxT("刷新"));
    m_saveButton = new wxButton(this, wxID_SAVE, wxT("保存到文件"));
    m_closeButton = new wxButton(this, wxID_CLOSE, wxT("关闭"));
    
    buttonSizer->Add(m_refreshButton, 0, wxALL, 5);
    buttonSizer->Add(m_saveButton, 0, wxALL, 5);
    buttonSizer->AddStretchSpacer();
    buttonSizer->Add(m_closeButton, 0, wxALL, 5);
    
    mainSizer->Add(buttonSizer, 0, wxALL | wxEXPAND, 10);
    
    SetSizer(mainSizer);
    Centre();
    
    // Load system info on startup
    wxString info = GetSystemInfo();
    m_infoText->SetValue(info);
}

void SystemInfoDialog::OnRefresh(wxCommandEvent& WXUNUSED(event))
{
    m_infoText->SetValue(wxT("正在获取系统信息..."));
    m_refreshButton->Enable(false);
    
    wxString info = GetSystemInfo();
    m_infoText->SetValue(info);
    
    m_refreshButton->Enable(true);
}

void SystemInfoDialog::OnSave(wxCommandEvent& WXUNUSED(event))
{
    wxString content = m_infoText->GetValue();
    if (SaveToFile(content)) {
        wxMessageBox(wxT("系统信息已保存到文件"), wxT("保存成功"), 
                     wxOK | wxICON_INFORMATION, this);
    }
}

void SystemInfoDialog::OnClose(wxCommandEvent& WXUNUSED(event))
{
    EndModal(wxID_CLOSE);
}

wxString SystemInfoDialog::GetSystemInfo()
{
    wxString info;

    // Try multiple approaches to get system information

    // Method 1: Try to execute script with combined stdout/stderr
    // Try to find server-info script in multiple locations
    wxString scriptPath;

    // Get the directory where the executable is located
    wxString exePath = wxStandardPaths::Get().GetExecutablePath();
    wxString exeDir = wxFileName(exePath).GetPath();

    // Try different possible locations for the script
    wxArrayString possiblePaths;
    possiblePaths.Add(exeDir + wxT("/server-info"));                    // Same directory as executable
    possiblePaths.Add(exeDir + wxT("/../server-info"));                // Parent directory
    possiblePaths.Add(wxT("./server-info"));                           // Current working directory
    possiblePaths.Add(wxT("/usr/local/bin/server-info"));              // System installation
    possiblePaths.Add(wxT("/usr/bin/server-info"));                    // System installation
    possiblePaths.Add(wxT("/home/<USER>/Nuctech_Services/OSS_MR/server-info")); // Original path (fallback)

    // Find the first existing script
    for (size_t i = 0; i < possiblePaths.GetCount(); i++) {
        if (wxFileName::FileExists(possiblePaths[i])) {
            scriptPath = possiblePaths[i];
            break;
        }
    }

    if (wxFileName::FileExists(scriptPath)) {
        // Execute the script (it will generate /tmp/server-info.txt)
        wxString command = scriptPath;

        long result = wxExecute(command, wxEXEC_SYNC);

        if (result == 0) {
            // Read the output file generated by the script
            wxString outputFile = wxT("/tmp/server-info.txt");

            if (wxFileName::FileExists(outputFile)) {
                std::ifstream file(outputFile.mb_str());
                if (file.is_open()) {
                    wxString scriptOutput;
                    std::string line;
                    while (std::getline(file, line)) {
                        scriptOutput += wxString(line.c_str(), wxConvUTF8) + wxT("\n");
                    }
                    file.close();

                    if (!scriptOutput.IsEmpty()) {
                        // Remove ANSI color codes
                        scriptOutput.Replace(wxT("\033[0;32m"), wxT(""));  // Green
                        scriptOutput.Replace(wxT("\033[1;33m"), wxT(""));  // Yellow
                        scriptOutput.Replace(wxT("\033[0;31m"), wxT(""));  // Red
                        scriptOutput.Replace(wxT("\033[0m"), wxT(""));     // No Color
                        return scriptOutput;
                    }
                }
            }
        }
    }

    // Method 2: Fallback to basic system information without requiring root
    info += wxT("=== 基本系统信息 ===\n\n");
    info += wxT("注意：某些详细硬件信息需要管理员权限\n\n");

    // Get current date and time
    wxDateTime now = wxDateTime::Now();
    info += wxT("查询时间：") + now.Format(wxT("%Y年%m月%d日 %H:%M:%S")) + wxT("\n\n");

    // Get basic system info that doesn't require root
    wxArrayString output;

    // OS Information
    output.Clear();
    if (wxExecute(wxT("lsb_release -d"), output, wxEXEC_SYNC) == 0 && !output.IsEmpty()) {
        wxString osInfo = output[0];
        if (osInfo.Contains(wxT(":"))) {
            osInfo = osInfo.AfterFirst(':').Strip(wxString::both);
        }
        info += wxT("操作系统：") + osInfo + wxT("\n");
    } else {
        // Fallback to /etc/os-release
        output.Clear();
        if (wxExecute(wxT("cat /etc/os-release | grep PRETTY_NAME"), output, wxEXEC_SYNC) == 0 && !output.IsEmpty()) {
            wxString osInfo = output[0];
            if (osInfo.Contains(wxT("="))) {
                osInfo = osInfo.AfterFirst('=').Strip(wxString::both);
                osInfo.Replace(wxT("\""), wxT(""));
            }
            info += wxT("操作系统：") + osInfo + wxT("\n");
        }
    }

    output.Clear();
    if (wxExecute(wxT("uname -r"), output, wxEXEC_SYNC) == 0 && !output.IsEmpty()) {
        info += wxT("内核版本：") + output[0] + wxT("\n");
    }

    // Hostname
    output.Clear();
    if (wxExecute(wxT("hostname"), output, wxEXEC_SYNC) == 0 && !output.IsEmpty()) {
        info += wxT("主机名：") + output[0] + wxT("\n");
    }

    // System uptime
    output.Clear();
    if (wxExecute(wxT("uptime -p"), output, wxEXEC_SYNC) == 0 && !output.IsEmpty()) {
        info += wxT("系统运行时间：") + output[0] + wxT("\n");
    }

    info += wxT("\n");

    // CPU Information
    output.Clear();
    if (wxExecute(wxT("lscpu | grep 'Model name'"), output, wxEXEC_SYNC) == 0 && !output.IsEmpty()) {
        wxString cpuModel = output[0];
        if (cpuModel.Contains(wxT(":"))) {
            cpuModel = cpuModel.AfterFirst(':').Strip(wxString::both);
        }
        info += wxT("CPU型号：") + cpuModel + wxT("\n");
    }

    output.Clear();
    if (wxExecute(wxT("nproc"), output, wxEXEC_SYNC) == 0 && !output.IsEmpty()) {
        info += wxT("CPU核心数：") + output[0] + wxT("\n");
    }

    // CPU frequency
    output.Clear();
    if (wxExecute(wxT("lscpu | grep 'CPU MHz'"), output, wxEXEC_SYNC) == 0 && !output.IsEmpty()) {
        wxString cpuFreq = output[0];
        if (cpuFreq.Contains(wxT(":"))) {
            cpuFreq = cpuFreq.AfterFirst(':').Strip(wxString::both);
            info += wxT("CPU频率：") + cpuFreq + wxT(" MHz\n");
        }
    }

    info += wxT("\n");

    // Memory Information
    output.Clear();
    if (wxExecute(wxT("free -h"), output, wxEXEC_SYNC) == 0 && output.GetCount() >= 2) {
        // Parse the memory line (usually the second line)
        for (size_t i = 0; i < output.GetCount(); i++) {
            if (output[i].Contains(wxT("Mem:"))) {
                wxString memLine = output[i];
                // Remove extra spaces and split
                memLine.Replace(wxT("  "), wxT(" "));
                memLine.Replace(wxT("  "), wxT(" "));
                wxArrayString parts = wxSplit(memLine, ' ', '\0');

                // Remove empty parts
                wxArrayString cleanParts;
                for (size_t j = 0; j < parts.GetCount(); j++) {
                    if (!parts[j].IsEmpty()) {
                        cleanParts.Add(parts[j]);
                    }
                }

                if (cleanParts.GetCount() >= 4) {
                    info += wxT("总内存：") + cleanParts[1] + wxT("\n");
                    info += wxT("已用内存：") + cleanParts[2] + wxT("\n");
                    info += wxT("可用内存：") + cleanParts[3] + wxT("\n");
                }
                break;
            }
        }
    } else {
        // Fallback method
        output.Clear();
        if (wxExecute(wxT("cat /proc/meminfo | grep MemTotal"), output, wxEXEC_SYNC) == 0 && !output.IsEmpty()) {
            wxString memTotal = output[0];
            if (memTotal.Contains(wxT(":"))) {
                memTotal = memTotal.AfterFirst(':').Strip(wxString::both);
                info += wxT("总内存：") + memTotal + wxT("\n");
            }
        }
    }

    info += wxT("\n");

    // Disk Information
    info += wxT("磁盘使用情况：\n");
    output.Clear();
    if (wxExecute(wxT("df -h"), output, wxEXEC_SYNC) == 0 && !output.IsEmpty()) {
        // Show main filesystems
        for (size_t i = 1; i < output.GetCount(); i++) { // Skip header line
            wxString line = output[i];
            if (line.Contains(wxT("/dev/")) || line.StartsWith(wxT("/"))) {
                // Clean up the line
                line.Replace(wxT("  "), wxT(" "));
                line.Replace(wxT("  "), wxT(" "));
                wxArrayString parts = wxSplit(line, ' ', '\0');

                // Remove empty parts
                wxArrayString cleanParts;
                for (size_t j = 0; j < parts.GetCount(); j++) {
                    if (!parts[j].IsEmpty()) {
                        cleanParts.Add(parts[j]);
                    }
                }

                if (cleanParts.GetCount() >= 6) {
                    wxString device = cleanParts[0];
                    wxString total = cleanParts[1];
                    wxString used = cleanParts[2];
                    wxString available = cleanParts[3];
                    wxString percent = cleanParts[4];
                    wxString mountpoint = cleanParts[5];

                    // Only show important mount points
                    if (mountpoint == wxT("/") || mountpoint == wxT("/home") ||
                        mountpoint == wxT("/var") || mountpoint == wxT("/tmp")) {
                        info += wxString::Format(wxT("  %s: %s (已用 %s, 可用 %s, 使用率 %s)\n"),
                                               mountpoint, total, used, available, percent);
                    }
                }
            }
        }
    }

    info += wxT("\n");

    // Network Information
    info += wxT("网络接口：\n");
    output.Clear();
    if (wxExecute(wxT("ip addr show"), output, wxEXEC_SYNC) == 0) {
        wxString currentInterface;
        for (size_t i = 0; i < output.GetCount(); i++) {
            wxString line = output[i].Strip(wxString::both);

            // Check for interface name
            if (line.Contains(wxT(": ")) && (line.Contains(wxT("eth")) ||
                line.Contains(wxT("ens")) || line.Contains(wxT("enp")) ||
                line.Contains(wxT("wlan")) || line.Contains(wxT("lo")))) {

                // Extract interface name
                int colonPos = line.Find(':');
                if (colonPos != wxNOT_FOUND) {
                    wxString interfacePart = line.Left(colonPos);
                    int spacePos = interfacePart.Find(' ');
                    if (spacePos != wxNOT_FOUND) {
                        currentInterface = interfacePart.Mid(spacePos + 1);
                    }
                }
            }

            // Check for IP address
            if (line.Contains(wxT("inet ")) && !line.Contains(wxT("127.0.0.1")) && !currentInterface.IsEmpty()) {
                wxString ipPart = line;
                if (ipPart.Contains(wxT("inet "))) {
                    ipPart = ipPart.AfterFirst(' ').BeforeFirst(' ');
                    info += wxString::Format(wxT("  %s: %s\n"), currentInterface, ipPart);
                }
            }
        }
    } else {
        // Fallback method
        output.Clear();
        if (wxExecute(wxT("hostname -I"), output, wxEXEC_SYNC) == 0 && !output.IsEmpty()) {
            info += wxT("  IP地址: ") + output[0].Strip(wxString::both) + wxT("\n");
        }
    }

    // Load average
    output.Clear();
    if (wxExecute(wxT("uptime"), output, wxEXEC_SYNC) == 0 && !output.IsEmpty()) {
        wxString uptimeLine = output[0];
        if (uptimeLine.Contains(wxT("load average:"))) {
            wxString loadAvg = uptimeLine.AfterLast(':').Strip(wxString::both);
            info += wxT("\n系统负载：") + loadAvg + wxT("\n");
        }
    }

    info += wxT("\n提示：要获取完整的硬件信息（主板、详细CPU规格等），请以管理员权限运行程序。\n");

    return info;
}

bool SystemInfoDialog::SaveToFile(const wxString& content)
{
    // Generate default filename with timestamp
    wxDateTime now = wxDateTime::Now();
    wxString defaultName = wxString::Format(wxT("server-info-%s.txt"), 
                                           now.Format(wxT("%Y%m%d_%H%M%S")));
    
    wxFileDialog saveDialog(this, wxT("保存系统信息"), wxEmptyString, defaultName,
                           wxT("文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*"),
                           wxFD_SAVE | wxFD_OVERWRITE_PROMPT);
    
    if (saveDialog.ShowModal() == wxID_OK) {
        wxString path = saveDialog.GetPath();
        
        wxFile file(path, wxFile::write);
        if (file.IsOpened()) {
            file.Write(content);
            file.Close();
            return true;
        } else {
            wxMessageBox(wxT("无法创建文件：") + path, wxT("保存失败"), 
                         wxOK | wxICON_ERROR, this);
            return false;
        }
    }
    
    return false;
}
