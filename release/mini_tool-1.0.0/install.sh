#!/bin/bash

# Mini Tool 安装脚本

echo "=== Mini Tool 安装脚本 ==="

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    INSTALL_DIR="/usr/local/bin"
    echo "检测到root权限，将安装到系统目录: $INSTALL_DIR"
else
    INSTALL_DIR="$HOME/.local/bin"
    echo "普通用户安装，将安装到用户目录: $INSTALL_DIR"
    mkdir -p "$INSTALL_DIR"
fi

# 获取当前目录
CURRENT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 复制文件
echo "复制程序文件..."
cp "$CURRENT_DIR/mini_tool" "$INSTALL_DIR/"
chmod +x "$INSTALL_DIR/mini_tool"

if [ -f "$CURRENT_DIR/server-info" ]; then
    cp "$CURRENT_DIR/server-info" "$INSTALL_DIR/"
    chmod +x "$INSTALL_DIR/server-info"
fi

echo "安装完成！"
echo "程序已安装到: $INSTALL_DIR/mini_tool"

# 检查PATH
if [[ ":$PATH:" != *":$INSTALL_DIR:"* ]]; then
    echo ""
    echo "注意: $INSTALL_DIR 不在您的 PATH 中"
    echo "请将以下行添加到您的 ~/.bashrc 或 ~/.profile 文件中："
    echo "export PATH=\"\$PATH:$INSTALL_DIR\""
    echo ""
    echo "然后运行: source ~/.bashrc"
fi

echo ""
echo "现在您可以在任何地方运行: mini_tool"

