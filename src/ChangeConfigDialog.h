#ifndef CHANGECONFIGDIALOG_H
#define CHANGECONFIGDIALOG_H

#include <wx/wx.h>
#include <wx/spinctrl.h>
#include <wx/choice.h>

class ChangeConfigDialog : public wxDialog
{
public:
    ChangeConfigDialog(wxWindow* parent);

private:
    // Event handlers
    void OnOK(wxCommandEvent& event);
    void OnCancel(wxCommandEvent& event);
    
    // Configuration file operations
    bool UpdateMCBCtrlIni(int detectorCount, const wxString& binFileName);
    bool UpdateLaunchFile(int detectorCount);
    bool UpdateJetResultMsg(int valveGroupCount);
    bool UpdateCImgDataBufferH(int detectorCount);
    bool UpdateMgsFlowCpp(int detectorCount);
    bool UpdateShareMemDefineH(int valveGroupCount);

    // Helper functions
    bool ReadFileContent(const wxString& filePath, wxString& content);
    bool WriteFileContent(const wxString& filePath, const wxString& content);
    int CalculateArraySize(int valveGroupCount);
    void ScanBinFiles();
    wxString GetCurrentBinFile();
    bool StartCompilation();
    
    // UI controls
    wxSpinCtrl* m_detectorCountSpin;
    wxChoice* m_fourPortChoice;
    wxSpinCtrl* m_valveGroupCountSpin;
    wxChoice* m_binFileChoice;
    wxButton* m_okButton;
    wxButton* m_cancelButton;
    
    // Button IDs
    enum
    {
        ID_OK_BTN = 2001,
        ID_CANCEL_BTN = 2002
    };

    wxDECLARE_EVENT_TABLE();
};

#endif // CHANGECONFIGDIALOG_H
