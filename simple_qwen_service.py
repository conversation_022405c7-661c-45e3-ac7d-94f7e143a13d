#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版 Qwen-Agent 服务
专注于核心聊天功能，避免复杂依赖
"""

import os
import json
import time
import logging
from typing import Dict, List, Optional
from flask import Flask, request, jsonify, Response
from flask_cors import CORS
import requests
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

class SimpleQwenService:
    """简化版 Qwen 服务"""
    
    def __init__(self):
        self.chat_sessions = {}
        self.ollama_url = "http://localhost:11434/api/chat"
        self.model_name = "qwen3:8b"
        
    def create_session(self, session_id: str = None) -> str:
        """创建新的聊天会话"""
        if not session_id:
            session_id = f"session_{int(time.time())}"
        
        self.chat_sessions[session_id] = {
            'messages': [],
            'created_at': datetime.now(),
            'last_activity': datetime.now()
        }
        
        logger.info(f"创建聊天会话: {session_id}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict]:
        """获取聊天会话"""
        return self.chat_sessions.get(session_id)
    
    def update_session_activity(self, session_id: str):
        """更新会话活动时间"""
        if session_id in self.chat_sessions:
            self.chat_sessions[session_id]['last_activity'] = datetime.now()
    
    def chat(self, message: str, session_id: str = None) -> Dict:
        """处理聊天请求"""
        # 获取或创建会话
        if not session_id:
            session_id = self.create_session()
        elif session_id not in self.chat_sessions:
            self.create_session(session_id)
        
        session = self.get_session(session_id)
        self.update_session_activity(session_id)
        
        # 添加用户消息
        user_message = {'role': 'user', 'content': message}
        session['messages'].append(user_message)
        
        try:
            # 调用 Ollama API
            response = self._call_ollama(session['messages'])
            
            if response:
                # 添加AI回复到会话
                session['messages'].append(response)
                return {
                    'success': True,
                    'response': response,
                    'session_id': session_id
                }
            else:
                error_response = {
                    'role': 'assistant',
                    'content': '抱歉，我现在无法处理您的请求，请稍后再试。'
                }
                session['messages'].append(error_response)
                return {
                    'success': False,
                    'response': error_response,
                    'session_id': session_id,
                    'error': 'Ollama API 调用失败'
                }
                
        except Exception as e:
            logger.error(f"聊天处理失败: {e}")
            error_response = {
                'role': 'assistant',
                'content': f'处理请求时出现错误：{str(e)}'
            }
            session['messages'].append(error_response)
            return {
                'success': False,
                'response': error_response,
                'session_id': session_id,
                'error': str(e)
            }
    
    def _call_ollama(self, messages: List[Dict]) -> Optional[Dict]:
        """调用 Ollama API"""
        try:
            # 构建请求数据
            request_data = {
                "model": self.model_name,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.8,
                    "max_tokens": 2000
                }
            }
            
            # 发送请求
            response = requests.post(
                self.ollama_url,
                json=request_data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'message' in result:
                    return result['message']
                else:
                    logger.error(f"Ollama 响应格式错误: {result}")
                    return None
            else:
                logger.error(f"Ollama API 错误: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Ollama API 请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"调用 Ollama 时出现异常: {e}")
            return None
    
    def get_chat_history(self, session_id: str) -> List[Dict]:
        """获取聊天历史"""
        session = self.get_session(session_id)
        return session['messages'] if session else []
    
    def clear_chat_history(self, session_id: str) -> bool:
        """清空聊天历史"""
        if session_id in self.chat_sessions:
            self.chat_sessions[session_id]['messages'] = []
            return True
        return False

# 全局服务实例
qwen_service = SimpleQwenService()

# API 路由定义
@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    # 检查 Ollama 服务
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        ollama_status = response.status_code == 200
    except:
        ollama_status = False
    
    return jsonify({
        'status': 'ok',
        'initialized': True,
        'ollama_available': ollama_status,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/chat', methods=['POST'])
def chat():
    """聊天接口"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据为空'}), 400
        
        message = data.get('message', '').strip()
        if not message:
            return jsonify({'error': '消息内容为空'}), 400
        
        session_id = data.get('session_id')
        
        # 处理聊天
        result = qwen_service.chat(message, session_id)
        
        return jsonify(result)
    
    except Exception as e:
        logger.error(f"聊天接口错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/sessions', methods=['POST'])
def create_session():
    """创建新会话"""
    session_id = qwen_service.create_session()
    return jsonify({
        'success': True,
        'session_id': session_id
    })

@app.route('/sessions/<session_id>/history', methods=['GET'])
def get_history(session_id):
    """获取会话历史"""
    history = qwen_service.get_chat_history(session_id)
    return jsonify({
        'success': True,
        'history': history
    })

@app.route('/sessions/<session_id>/clear', methods=['POST'])
def clear_history(session_id):
    """清空会话历史"""
    success = qwen_service.clear_chat_history(session_id)
    return jsonify({
        'success': success
    })

if __name__ == '__main__':
    logger.info("启动简化版 Qwen-Agent 服务...")
    
    # 检查 Ollama 服务
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            logger.info("✅ Ollama 服务连接正常")
        else:
            logger.warning("⚠️  Ollama 服务响应异常")
    except Exception as e:
        logger.warning(f"⚠️  无法连接到 Ollama 服务: {e}")
    
    # 启动服务
    app.run(
        host='127.0.0.1',
        port=5000,
        debug=False,
        threaded=True
    )
