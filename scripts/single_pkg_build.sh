#!/bin/bash

# Nuctech OSS System Build Script
# This script builds the mgspkg package for the OSS system

set -e  # Exit on any error

# Function to print colored output
print_info() {
    echo -e "\033[1;34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[1;32m[SUCCESS]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[1;33m[WARNING]\033[0m $1"
}

# Check if we're in the correct directory
if [ ! -d "/home/<USER>/Nuctech_Services/OSS_MR" ]; then
    print_error "OSS_MR directory not found at /home/<USER>/Nuctech_Services/OSS_MR"
    print_error "Please ensure the Nuctech OSS system is properly installed."
    exit 1
fi

# Change to the OSS_MR directory
cd /home/<USER>/Nuctech_Services/OSS_MR

print_info "Starting build process in $(pwd)"

# Check if the original build script exists
if [ -f "./single_pkg_build.sh" ]; then
    print_info "Found original build script, executing..."
    bash ./single_pkg_build.sh "$@"
    exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        print_success "Build completed successfully!"
    else
        print_error "Build failed with exit code $exit_code"
    fi
    
    exit $exit_code
else
    print_warning "Original build script not found, attempting manual build..."
    
    # Manual build process
    print_info "Building mgspkg package..."
    
    # Check for common build tools
    if command -v make >/dev/null 2>&1; then
        print_info "Using make to build..."
        make mgspkg
    elif command -v cmake >/dev/null 2>&1; then
        print_info "Using cmake to build..."
        if [ ! -d "build" ]; then
            mkdir build
        fi
        cd build
        cmake ..
        make mgspkg
    else
        print_error "No suitable build system found (make/cmake)"
        exit 1
    fi
    
    print_success "Manual build completed!"
fi
