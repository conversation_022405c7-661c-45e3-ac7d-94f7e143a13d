#ifndef DELETESOURCEDIALOG_H
#define DELETESOURCEDIALOG_H

#include <wx/wx.h>

class DeleteSourceDialog : public wxDialog
{
public:
    DeleteSourceDialog(wxWindow* parent);

private:
    // Event handlers
    void OnConfirm(wxCommandEvent& event);
    void OnCancel(wxCommandEvent& event);
    
    // Helper methods
    wxString FindOSSMRPath();
    bool BackupOSSMR();
    bool DeleteCppFiles();
    int DeleteCppFilesRecursive(const wxString& dir, wxString& results, bool& allSuccess);
    void ShowResult(bool success, const wxString& message);
    
    // UI components
    wxStaticText* m_warningText;
    wxStaticText* m_detailText;
    wxButton* m_confirmButton;
    wxButton* m_cancelButton;
    
    // Constants
    static const wxString OSS_MR_PATH;
    
    wxDECLARE_EVENT_TABLE();
};

#endif // DELETESOURCEDIALOG_H
