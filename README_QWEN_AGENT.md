# Qwen-Agent 集成指南

本文档介绍如何在 MiniTool 项目中集成和使用 Qwen-Agent 服务。

## 🎯 **集成概述**

我们采用了 **方案1: Python 服务层 + HTTP API** 的架构：

```
C++ wxWidgets 前端 ←→ HTTP API ←→ Python Qwen-Agent 服务
```

### **优势**
- ✅ 保持现有 C++ 代码结构不变
- ✅ Python 服务可独立部署和更新
- ✅ 支持多客户端连接
- ✅ 易于调试和维护
- ✅ 更强大的 AI 能力和工具调用

## 🚀 **快速开始**

### **1. 安装依赖**

运行自动安装脚本：

```bash
./setup_qwen_agent.sh
```

或手动安装：

```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 设置权限
chmod +x start_qwen_service.sh
chmod +x test_qwen_integration.py
```

### **2. 启动 Qwen-Agent 服务**

```bash
./start_qwen_service.sh
```

服务启动后会显示：
- 服务地址: http://127.0.0.1:5000
- 健康检查: http://127.0.0.1:5000/health

### **3. 测试服务**

在另一个终端运行测试：

```bash
python3 test_qwen_integration.py
```

### **4. 编译并运行 MiniTool**

```bash
cd build
make
./mini_tool
```

## 📋 **系统要求**

### **必需组件**
- Python 3.8+
- pip3
- curl
- wxWidgets 3.0+
- CMake 3.16+

### **可选组件**
- Ollama (用于本地 LLM 服务)
- qwen3:8b 模型

## 🔧 **配置说明**

### **Qwen-Agent 服务配置**

编辑 `qwen_agent_service.py` 中的配置：

```python
# LLM 配置
llm_cfg = {
    'model': 'qwen3:8b',  # 模型名称
    'model_server': 'http://localhost:11434/v1',  # Ollama 服务地址
    'api_key': 'EMPTY',
    'generate_cfg': {
        'top_p': 0.8,
        'temperature': 0.7,
        'max_tokens': 2000,
    }
}

# 工具配置
tools = [
    {
        'mcpServers': {
            'filesystem': {
                'command': 'python3',
                'args': ['mcp_file_server.py']
            }
        }
    },
    'code_interpreter'  # 内置代码解释器
]
```

### **C++ 客户端配置**

在 `src/AiChatDialog.cpp` 中的常量定义：

```cpp
const wxString QWEN_AGENT_API_URL = wxT("http://127.0.0.1:5000/chat");
const wxString QWEN_AGENT_HEALTH_URL = wxT("http://127.0.0.1:5000/health");
```

## 🛠️ **API 接口**

### **健康检查**
```
GET /health
```

### **创建会话**
```
POST /sessions
```

### **聊天接口**
```
POST /chat
Content-Type: application/json

{
    "message": "用户消息",
    "session_id": "会话ID",
    "stream": false
}
```

### **获取历史记录**
```
GET /sessions/{session_id}/history
```

### **清空历史记录**
```
POST /sessions/{session_id}/clear
```

## 🔍 **故障排除**

### **常见问题**

1. **服务启动失败**
   ```bash
   # 检查 Python 环境
   python3 --version
   pip3 --version
   
   # 检查依赖安装
   pip list | grep qwen-agent
   ```

2. **Ollama 连接失败**
   ```bash
   # 启动 Ollama 服务
   ollama serve
   
   # 安装模型
   ollama pull qwen3:8b
   
   # 测试连接
   curl http://localhost:11434/api/tags
   ```

3. **MCP 文件服务器问题**
   ```bash
   # 检查文件权限
   ls -la mcp_file_server.py
   
   # 手动测试
   python3 mcp_file_server.py --help
   ```

4. **编译错误**
   ```bash
   # 清理并重新编译
   cd build
   make clean
   make
   ```

### **日志查看**

- Qwen-Agent 服务日志: `qwen_agent_service.log`
- C++ 应用日志: 控制台输出

## 🎨 **功能特性**

### **已实现功能**
- ✅ 基本聊天对话
- ✅ 会话管理
- ✅ 文件操作工具 (通过 MCP)
- ✅ 代码解释器
- ✅ 错误处理和重试机制

### **计划功能**
- 🔄 流式响应支持
- 🔄 RAG 文档问答
- 🔄 更多 MCP 服务器集成
- 🔄 并行工具调用

## 📚 **开发指南**

### **添加新工具**

1. 在 `qwen_agent_service.py` 中添加工具配置：

```python
tools = [
    {
        'mcpServers': {
            'new_tool': {
                'command': 'python3',
                'args': ['new_tool_server.py']
            }
        }
    }
]
```

2. 实现对应的 MCP 服务器

### **修改 UI**

C++ 前端的 UI 逻辑在 `src/AiChatDialog.cpp` 中，主要方法：
- `SendMessageToQwenAgent()`: 发送消息
- `ProcessQwenAgentResponse()`: 处理响应
- `CheckQwenAgentServiceStatus()`: 检查服务状态

## 📄 **许可证**

本集成遵循原项目的许可证条款。

## 🤝 **贡献**

欢迎提交 Issue 和 Pull Request 来改进这个集成。

---

**注意**: 这是一个实验性集成，建议在生产环境使用前进行充分测试。
