#ifndef MCPCLIENT_H
#define MCPCLIENT_H

#include <wx/wx.h>
#include <wx/process.h>
#include <wx/stream.h>
#include <wx/txtstrm.h>
#include <vector>
#include <map>
#include <memory>
#include "json.hpp"

using json = nlohmann::json;

// MCP工具定义结构
struct MCPTool {
    wxString name;
    wxString description;
    json inputSchema;
    
    MCPTool(const wxString& n, const wxString& desc, const json& schema)
        : name(n), description(desc), inputSchema(schema) {}
};

// MCP工具调用请求
struct MCPToolCall {
    wxString name;
    json arguments;
    wxString callId;  // 用于跟踪调用
    
    MCPToolCall(const wxString& n, const json& args, const wxString& id = wxEmptyString)
        : name(n), arguments(args), callId(id) {}
};

// MCP工具调用结果
struct MCPToolResult {
    wxString callId;
    bool isError;
    wxString content;
    
    MCPToolResult(const wxString& id, bool error, const wxString& result)
        : callId(id), isError(error), content(result) {}
};

class MCPClient
{
public:
    MCPClient();
    ~MCPClient();

    // 启动MCP服务器
    bool StartServer(const wxString& command, const wxArrayString& args);
    
    // 停止MCP服务器
    void StopServer();
    
    // 检查服务器是否运行
    bool IsServerRunning() const;
    
    // 初始化MCP连接
    bool Initialize();
    
    // 获取可用工具列表
    std::vector<MCPTool> GetAvailableTools();
    
    // 调用工具
    MCPToolResult CallTool(const MCPToolCall& toolCall);
    
    // 生成工具描述文本（用于AI提示）
    wxString GenerateToolsDescription() const;

private:
    // JSON-RPC通信
    bool SendRequest(const json& request, json& response);
    bool SendNotification(const json& notification);
    
    // 协议初始化
    bool SendInitialize();
    bool SendInitialized();
    
    // 工具相关
    bool ListTools();
    
    // 进程管理
    wxProcess* m_serverProcess;
    long m_serverPid;
    
    // 通信管道
    wxInputStream* m_inputStream;
    wxOutputStream* m_outputStream;
    
    // 状态管理
    bool m_initialized;
    bool m_serverRunning;
    
    // 工具缓存
    std::vector<MCPTool> m_availableTools;
    
    // 请求ID管理
    int m_nextRequestId;
    
    // 辅助方法
    wxString ReadFromServer();
    bool WriteToServer(const wxString& data);
    json CreateRequest(const wxString& method, const json& params = json::object());
    json CreateNotification(const wxString& method, const json& params = json::object());
    
    // 常量
    static const wxString MCP_VERSION;
    static const int TIMEOUT_MS;
};

#endif // MCPCLIENT_H
