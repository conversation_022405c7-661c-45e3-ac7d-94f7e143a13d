#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Qwen-Agent 集成的脚本
"""

import requests
import json
import time
import sys

def test_health_check():
    """测试健康检查接口"""
    print("🔍 测试健康检查接口...")
    try:
        response = requests.get("http://127.0.0.1:5000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查成功: {data}")
            return data.get('initialized', False)
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_create_session():
    """测试创建会话"""
    print("\n📝 测试创建会话...")
    try:
        response = requests.post("http://127.0.0.1:5000/sessions", timeout=10)
        if response.status_code == 200:
            data = response.json()
            session_id = data.get('session_id')
            print(f"✅ 会话创建成功: {session_id}")
            return session_id
        else:
            print(f"❌ 会话创建失败: HTTP {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 会话创建异常: {e}")
        return None

def test_chat(session_id, message):
    """测试聊天功能"""
    print(f"\n💬 测试聊天: {message}")
    try:
        payload = {
            "message": message,
            "session_id": session_id,
            "stream": False
        }
        
        response = requests.post(
            "http://127.0.0.1:5000/chat", 
            json=payload, 
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                ai_response = data.get('response', {}).get('content', '')
                print(f"✅ AI回复: {ai_response[:100]}...")
                return True
            else:
                print(f"❌ 聊天失败: {data}")
                return False
        else:
            print(f"❌ 聊天请求失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 聊天异常: {e}")
        return False

def test_get_history(session_id):
    """测试获取历史记录"""
    print(f"\n📚 测试获取历史记录...")
    try:
        response = requests.get(f"http://127.0.0.1:5000/sessions/{session_id}/history", timeout=10)
        if response.status_code == 200:
            data = response.json()
            history = data.get('history', [])
            print(f"✅ 历史记录获取成功，共 {len(history)} 条消息")
            return True
        else:
            print(f"❌ 历史记录获取失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 历史记录获取异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 Qwen-Agent 集成")
    print("=" * 50)
    
    # 1. 健康检查
    if not test_health_check():
        print("\n❌ 服务未就绪，请先启动 Qwen-Agent 服务")
        print("运行命令: ./start_qwen_service.sh")
        sys.exit(1)
    
    # 2. 创建会话
    session_id = test_create_session()
    if not session_id:
        print("\n❌ 无法创建会话，测试终止")
        sys.exit(1)
    
    # 3. 测试基本聊天
    test_messages = [
        "你好，请介绍一下你自己",
        "今天天气怎么样？",
        "帮我列出当前目录的文件"
    ]
    
    success_count = 0
    for message in test_messages:
        if test_chat(session_id, message):
            success_count += 1
        time.sleep(2)  # 避免请求过快
    
    # 4. 测试历史记录
    test_get_history(session_id)
    
    # 5. 总结
    print("\n" + "=" * 50)
    print(f"🎯 测试完成: {success_count}/{len(test_messages)} 个聊天测试成功")
    
    if success_count == len(test_messages):
        print("✅ 所有测试通过！Qwen-Agent 集成成功")
        return 0
    else:
        print("⚠️  部分测试失败，请检查服务配置")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
