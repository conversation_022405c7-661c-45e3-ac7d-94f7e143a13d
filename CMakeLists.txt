cmake_minimum_required(VERSION 3.16)
project(mini_tool)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find wxWidgets using wx-config
execute_process(COMMAND wx-config --cxxflags OUTPUT_VARIABLE WX_CXX_FLAGS OUTPUT_STRIP_TRAILING_WHITESPACE)
execute_process(COMMAND wx-config --libs OUTPUT_VARIABLE WX_LIBS OUTPUT_STRIP_TRAILING_WHITESPACE)

# Source files
set(SOURCES
    src/main.cpp
    src/MainFrame.cpp
    src/LoginDialog.cpp
    src/DeleteSourceDialog.cpp
    src/SystemInfoDialog.cpp
    src/UdpTestDialog.cpp
    src/ChangeConfigDialog.cpp
    src/ReplaceAlgorithmDialog.cpp
    src/ProgressDialog.cpp
    src/AiChatDialog.cpp
    src/MCPClient.cpp
)

# Header files
set(HEADERS
    src/MainFrame.h
    src/LoginDialog.h
    src/DeleteSourceDialog.h
    src/SystemInfoDialog.h
    src/UdpTestDialog.h
    src/ChangeConfigDialog.h
    src/ReplaceAlgorithmDialog.h
    src/ProgressDialog.h
    src/AiChatDialog.h
    src/MCPClient.h
)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Set compiler flags and link libraries
set_target_properties(${PROJECT_NAME} PROPERTIES COMPILE_FLAGS "${WX_CXX_FLAGS}")
target_link_libraries(${PROJECT_NAME} ${WX_LIBS})

# Include directories
target_include_directories(${PROJECT_NAME} PRIVATE src)

# Additional compiler flags
if(CMAKE_COMPILER_IS_GNUCXX)
    target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra)
endif()

# Install target
install(TARGETS ${PROJECT_NAME} DESTINATION bin)

# Install build script
install(FILES scripts/single_pkg_build.sh
        DESTINATION bin
        PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE)
