#!/bin/bash
# 简化版 Qwen 服务启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 启动简化版 Qwen 服务 ===${NC}"

# 检查 Python 版本
echo -e "${YELLOW}检查 Python 环境...${NC}"
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}错误: 未找到 python3${NC}"
    exit 1
fi

PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "✅ Python 版本: $PYTHON_VERSION"

# 检查是否需要创建虚拟环境
if [ ! -d "simple_venv" ]; then
    echo -e "${YELLOW}创建虚拟环境...${NC}"
    python3 -m venv simple_venv
fi

# 激活虚拟环境
echo -e "${YELLOW}激活虚拟环境...${NC}"
source simple_venv/bin/activate

# 安装依赖
echo -e "${YELLOW}安装依赖包...${NC}"
pip install -r simple_requirements.txt

# 检查 Ollama 服务
echo -e "${YELLOW}检查 Ollama 服务...${NC}"
if curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "✅ Ollama 服务正在运行"
    
    # 检查 qwen3:8b 模型
    if curl -s http://localhost:11434/api/tags | grep -q "qwen3:8b"; then
        echo "✅ qwen3:8b 模型已安装"
    else
        echo -e "${YELLOW}⚠️  qwen3:8b 模型未安装${NC}"
        echo "建议运行: ollama pull qwen3:8b"
    fi
else
    echo -e "${RED}❌ Ollama 服务未运行${NC}"
    echo "请先启动 Ollama 服务: ollama serve"
    echo "然后安装模型: ollama pull qwen3:8b"
fi

# 启动服务
echo -e "\n${GREEN}启动简化版 Qwen 服务...${NC}"
echo "服务地址: http://127.0.0.1:5000"
echo "健康检查: http://127.0.0.1:5000/health"
echo "按 Ctrl+C 停止服务"
echo ""

python3 simple_qwen_service.py
