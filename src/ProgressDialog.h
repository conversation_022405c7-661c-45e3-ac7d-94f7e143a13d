#ifndef PROGRESSDIALOG_H
#define PROGRESSDIALOG_H

#include <wx/wx.h>
#include <wx/progdlg.h>
#include <wx/thread.h>

// Forward declaration
class CompressionThread;

class ProgressDialog : public wxDialog
{
    friend class CompressionThread;

public:
    ProgressDialog(wxWindow* parent, const wxString& title, const wxString& message);
    virtual ~ProgressDialog();

    // Start the compression process
    bool StartCompression(const wxString& sourceDir, const wxString& targetFile);

    // Get the result
    bool IsSuccess() const { return m_success; }
    wxString GetErrorMessage() const { return m_errorMessage; }

private:
    // UI components
    wxStaticText* m_messageText;
    wxGauge* m_progressBar;
    wxStaticText* m_statusText;
    wxButton* m_cancelButton;
    
    // Progress tracking
    bool m_success;
    bool m_cancelled;
    wxString m_errorMessage;
    
    // Event handlers
    void OnCancel(wxCommandEvent& event);
    void OnClose(wxCloseEvent& event);
    void OnTimer(wxTimerEvent& event);
    
    // Progress update methods
    void UpdateProgress(int percentage, const wxString& status);
    void SetCompleted(bool success, const wxString& message = wxEmptyString);
    
    // Timer for progress updates
    wxTimer* m_timer;
    
    // Thread-safe progress communication
    wxCriticalSection m_progressCS;
    int m_currentProgress;
    wxString m_currentStatus;
    bool m_completed;
    
    wxDECLARE_EVENT_TABLE();
};

// Worker thread for compression
class CompressionThread : public wxThread
{
public:
    CompressionThread(ProgressDialog* dialog, const wxString& sourceDir, const wxString& targetFile);
    
protected:
    virtual ExitCode Entry() override;
    
private:
    ProgressDialog* m_dialog;
    wxString m_sourceDir;
    wxString m_targetFile;
    
    // Helper methods
    bool CompressDirectory();
    void UpdateProgress(int percentage, const wxString& status);
    void SetCompleted(bool success, const wxString& message = wxEmptyString);
};

#endif // PROGRESSDIALOG_H
