#!/bin/bash
# Qwen-Agent 集成安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Qwen-Agent 集成安装脚本 ===${NC}"
echo -e "${BLUE}为 MiniTool 项目集成 Qwen-Agent 服务${NC}"
echo ""

# 检查系统要求
echo -e "${YELLOW}检查系统要求...${NC}"

# 检查 Python
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}错误: 未找到 python3${NC}"
    echo "请安装 Python 3.8 或更高版本"
    exit 1
fi

PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "✅ Python 版本: $PYTHON_VERSION"

# 检查 pip
if ! command -v pip3 &> /dev/null; then
    echo -e "${RED}错误: 未找到 pip3${NC}"
    echo "请安装 pip3"
    exit 1
fi
echo "✅ pip3 已安装"

# 检查 curl
if ! command -v curl &> /dev/null; then
    echo -e "${RED}错误: 未找到 curl${NC}"
    echo "请安装 curl: sudo apt-get install curl"
    exit 1
fi
echo "✅ curl 已安装"

# 创建虚拟环境
echo -e "\n${YELLOW}创建 Python 虚拟环境...${NC}"
if [ ! -d "venv" ]; then
    python3 -m venv venv
    echo "✅ 虚拟环境创建成功"
else
    echo "✅ 虚拟环境已存在"
fi

# 激活虚拟环境
echo -e "\n${YELLOW}激活虚拟环境...${NC}"
source venv/bin/activate

# 升级 pip
echo -e "\n${YELLOW}升级 pip...${NC}"
pip install --upgrade pip

# 安装依赖
echo -e "\n${YELLOW}安装 Qwen-Agent 依赖...${NC}"
pip install -r requirements.txt

# 检查安装
echo -e "\n${YELLOW}验证安装...${NC}"
python3 -c "import qwen_agent; print('✅ qwen-agent 安装成功')" || {
    echo -e "${RED}❌ qwen-agent 安装失败${NC}"
    exit 1
}

python3 -c "import flask; print('✅ flask 安装成功')" || {
    echo -e "${RED}❌ flask 安装失败${NC}"
    exit 1
}

# 检查 Ollama（可选）
echo -e "\n${YELLOW}检查 Ollama 服务...${NC}"
if command -v ollama &> /dev/null; then
    echo "✅ Ollama 已安装"
    
    # 检查 Ollama 服务是否运行
    if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
        echo "✅ Ollama 服务正在运行"
        
        # 检查 qwen3:8b 模型
        if ollama list | grep -q "qwen3:8b"; then
            echo "✅ qwen3:8b 模型已安装"
        else
            echo -e "${YELLOW}⚠️  qwen3:8b 模型未安装${NC}"
            echo "建议运行: ollama pull qwen3:8b"
        fi
    else
        echo -e "${YELLOW}⚠️  Ollama 服务未运行${NC}"
        echo "请运行: ollama serve"
    fi
else
    echo -e "${YELLOW}⚠️  Ollama 未安装${NC}"
    echo "请访问 https://ollama.ai 安装 Ollama"
fi

# 检查 MCP 文件服务器
echo -e "\n${YELLOW}检查 MCP 文件服务器...${NC}"
if [ -f "mcp_file_server.py" ]; then
    echo "✅ MCP 文件服务器存在"
    chmod +x mcp_file_server.py
else
    echo -e "${YELLOW}⚠️  MCP 文件服务器不存在${NC}"
    echo "文件操作功能可能不可用"
fi

# 设置权限
echo -e "\n${YELLOW}设置脚本权限...${NC}"
chmod +x start_qwen_service.sh
chmod +x test_qwen_integration.py
echo "✅ 权限设置完成"

# 编译 C++ 项目
echo -e "\n${YELLOW}编译 C++ 项目...${NC}"
if [ -d "build" ]; then
    cd build
    make
    cd ..
    echo "✅ C++ 项目编译完成"
else
    echo -e "${YELLOW}⚠️  build 目录不存在，请先运行 cmake${NC}"
fi

# 完成
echo -e "\n${GREEN}=== 安装完成 ===${NC}"
echo -e "${BLUE}接下来的步骤：${NC}"
echo ""
echo "1. 启动 Qwen-Agent 服务:"
echo "   ./start_qwen_service.sh"
echo ""
echo "2. 在另一个终端测试服务:"
echo "   python3 test_qwen_integration.py"
echo ""
echo "3. 运行 MiniTool 应用:"
echo "   ./build/mini_tool"
echo ""
echo -e "${GREEN}🎉 集成完成！现在可以在 MiniTool 中使用 Qwen-Agent 了${NC}"
