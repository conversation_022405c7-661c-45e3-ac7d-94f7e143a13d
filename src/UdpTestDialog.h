#ifndef UDPTESTDIALOG_H
#define UDPTESTDIALOG_H

#include <wx/wx.h>
#include <wx/socket.h>
#include <wx/timer.h>
#include <wx/spinctrl.h>
#include <wx/statbox.h>

class UdpTestDialog : public wxDialog
{
public:
    UdpTestDialog(wxWindow* parent);
    virtual ~UdpTestDialog();

private:
    // UI控件
    wxTextCtrl* m_localIpText;
    wxSpinCtrl* m_localPortSpin;
    wxTextCtrl* m_remoteIpText;
    wxSpinCtrl* m_remotePortSpin;
    wxButton* m_connectBtn;
    wxStaticText* m_statusText;
    
    // 控制参数
    wxSpinCtrl* m_valveCountSpin;
    wxSpinCtrl* m_blowDelaySpin;
    wxSpinCtrl* m_triggerTimeSpin;
    wxButton* m_startBtn;
    wxButton* m_stopBtn;

    // 单阀测试控件
    wxSpinCtrl* m_singleValveSpin;
    wxButton* m_singleValveBtn;

    // 连续测试控件
    wxSpinCtrl* m_continuousStartValveSpin;
    wxSpinCtrl* m_continuousEndValveSpin;
    wxButton* m_continuousTestBtn;
    wxStaticText* m_currentValveLabel;

    // 状态显示控件
    wxStaticText* m_statusBlowDelayLabel;
    wxStaticText* m_statusTriggerTimeLabel;
    wxStaticText* m_statusTotalTriggersLabel;
    wxStaticText* m_statusLostTriggersALabel;
    wxStaticText* m_statusTotalPacketsLabel;
    wxStaticText* m_statusLostPacketsALabel;
    wxStaticText* m_statusValveFreqLabel;
    wxStaticText* m_statusTotalExecutionsLabel;
    wxStaticText* m_statusLostTriggersBLabel;
    wxStaticText* m_statusLostPacketsBLabel;

    // 状态显示
    wxTextCtrl* m_logText;
    
    // 网络相关
    wxDatagramSocket* m_socket;
    wxIPV4address m_localAddr;
    wxIPV4address m_remoteAddr;
    bool m_connected;
    
    // 定时器
    wxTimer* m_sendTimer;
    
    // 控制状态
    bool m_isRunning;
    int m_totalPackets;

    // 单阀测试状态
    bool m_singleValveMode;
    int m_singleValveNumber;
    int m_singleValvePacketCount;
    bool m_singleValveState; // true=开启，false=关闭

    // 连续测试状态
    bool m_continuousTestMode;
    int m_currentTestValve;
    int m_continuousPacketCount;
    bool m_continuousValveState; // true=开启，false=关闭
    
    // 协议参数
    struct ControlPacket {
        uint32_t packetType;      // 0 表示采集控制包
        uint32_t enable;          // 1表示开始，0表示停止
        uint32_t blowDelay;       // 喷吹延时X，X*800us
        uint32_t triggerTime;     // 行触发时间(us) 800
        uint32_t totalPackets;    // 数据总包数
        uint32_t fpgaTriggerTime; // FPGA触发时间(暂未使用)
        uint32_t fpgaTriggerMode; // 1：外触发 0：内触发
        uint32_t fpgaDutyCycle;   // FPGA触发占空比
    };

    // 状态数据包结构体 (0x58)
    struct StatusPacket {
        uint32_t packetType;           // 0x58表示状态数据包
        uint32_t blowDelay;            // 喷吹延时X，X*800us
        uint32_t triggerTime;          // 行触发时间(us) 800
        uint32_t totalTriggerSignals;  // 接收触发信号总数
        uint32_t lostTriggerSignalsA;  // 丢失触发信号总数A（低于90ms）
        uint32_t totalDataPackets;     // 接收数据包总数
        uint32_t lostDataPacketsA;     // 丢失数据包总数A（少）
        uint32_t valveFrequency;       // 阀信号执行频率/秒
        uint32_t totalValveExecutions; // 阀执行总次数
        uint32_t lostTriggerSignalsB;  // 丢失触发信号总数B（高于110ms）
        uint32_t lostDataPacketsB;     // 丢失数据包总数B（多）
        uint32_t reserved;             // 待定
    };
    
    struct ValveDataPacket {
        uint32_t packetType;      // 1-7表示第几包阀数据
        uint32_t packetSize;      // 整个包的大小长度
        uint32_t blowDelay;       // 喷吹延时X
        uint32_t triggerTime;     // 行触发时间(us)
        uint32_t totalPackets;    // 数据总包数
        uint8_t reserved[12];     // 空字节
        uint8_t valveData[80 * 16]; // 阀数据，每个阀16字节
    };

    // 事件处理
    void OnConnect(wxCommandEvent& event);
    void OnStart(wxCommandEvent& event);
    void OnStop(wxCommandEvent& event);
    void OnSingleValveTest(wxCommandEvent& event);
    void OnContinuousTest(wxCommandEvent& event);
    void OnClose(wxCloseEvent& event);
    void OnTimer(wxTimerEvent& event);
    void OnSocketEvent(wxSocketEvent& event);
    void OnValveCountChanged(wxSpinEvent& event);
    
    // 辅助函数
    void CreateControls();
    void UpdateStatus(const wxString& status);
    void LogMessage(const wxString& message);
    bool ConnectSocket();
    void DisconnectSocket();
    int CalculateTotalPackets(int valveCount);
    void SendControlPacket(bool enable);
    void SendValveDataPacket(int packetIndex);
    void SendValveClosePacket(int packetIndex);
    void SendSingleValveDataPacket(int packetIndex);
    void SendSingleValveClosePacket(int packetIndex);
    void SendContinuousValveDataPacket(int packetIndex);
    void GenerateValveData(uint8_t* data, int valveStart, int valveCount);
    void GenerateSingleValveData(uint8_t* data, int valveStart, int valveCount, int targetValve, bool valveState);
    void GenerateContinuousValveData(uint8_t* data, int valveStart, int valveCount, int targetValve, bool valveState);
    void ParseStatusPacket(const uint8_t* data, size_t dataSize);
    void UpdateStatusDisplay(const StatusPacket& status);
    
    DECLARE_EVENT_TABLE()
};

// 事件ID
enum
{
    ID_CONNECT_BTN = 1000,
    ID_START_BTN,
    ID_STOP_BTN,
    ID_SINGLE_VALVE_BTN,
    ID_CONTINUOUS_TEST_BTN,
    ID_SEND_TIMER,
    ID_SOCKET_EVENT,
    ID_VALVE_COUNT_SPIN
};

#endif // UDPTESTDIALOG_H
