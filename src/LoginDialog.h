#pragma once

#include <wx/wx.h>
#include <wx/textctrl.h>

class LoginDialog : public wxDialog
{
public:
    LoginDialog(wxWindow* parent);
    
    // Event handlers
    void OnOK(wxCommandEvent& event);
    void OnCancel(wxCommandEvent& event);
    void OnTextEnter(wxCommandEvent& event);
    
    // Getters
    wxString GetUsername() const { return m_username; }
    wxString GetPassword() const { return m_password; }
    
    // Validation
    bool ValidateCredentials(const wxString& username, const wxString& password);

private:
    // U<PERSON> controls
    wxTextCtrl* m_usernameCtrl;
    wxTextCtrl* m_passwordCtrl;
    wxButton* m_okButton;
    wxButton* m_cancelButton;
    wxStaticText* m_errorLabel;
    
    // Data
    wxString m_username;
    wxString m_password;
    
    // Helper functions
    void CreateControls();
    void ShowError(const wxString& message);
    void ClearError();
    
    // Button IDs
    enum
    {
        ID_OK_BTN = 3001,
        ID_CANCEL_BTN = 3002,
        ID_USERNAME_CTRL = 3003,
        ID_PASSWORD_CTRL = 3004
    };
    
    wxDECLARE_EVENT_TABLE();
};
