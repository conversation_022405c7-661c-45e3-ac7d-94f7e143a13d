2025-07-04 17:12:21,878 - qwen_agent_logger - INFO - Initializing MCP tools from mcp servers: ['filesystem']
2025-07-04 17:12:21,880 - qwen_agent_logger - INFO - Initializing a MCP stdio_client, if this takes forever, please check the config of this mcp server: filesystem
2025-07-04 17:12:22,876 - matplotlib.font_manager - INFO - Failed to extract font properties from /usr/share/fonts/truetype/noto/NotoColorEmoji.ttf: Can not load face (unknown file format; error code 0x2)
2025-07-04 17:12:22,896 - matplotlib.font_manager - INFO - generated new fontManager
2025-07-04 17:12:23,388 - __main__ - INFO - Qwen-Agent 初始化成功
2025-07-04 17:12:23,389 - __main__ - INFO - 启动 Qwen-Agent 服务...
2025-07-04 17:12:23,390 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-04 17:12:23,390 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-04 17:14:59,209 - qwen_agent_logger - INFO - Initializing MCP tools from mcp servers: ['filesystem']
2025-07-04 17:14:59,211 - qwen_agent_logger - INFO - Initializing a MCP stdio_client, if this takes forever, please check the config of this mcp server: filesystem
2025-07-04 17:14:59,837 - __main__ - INFO - Qwen-Agent 初始化成功
2025-07-04 17:14:59,838 - __main__ - INFO - 启动 Qwen-Agent 服务...
2025-07-04 17:14:59,839 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-04 17:14:59,839 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
