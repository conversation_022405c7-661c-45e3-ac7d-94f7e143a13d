#!/bin/bash

# MCP File Server启动脚本

HERE="$(dirname "$(readlink -f "${0}")")"

# 设置Python路径
export PYTHONPATH="${HERE}/../share/mini_tool:${HERE}:${PYTHONPATH}"

# 尝试多个可能的Python文件位置
PYTHON_SCRIPT=""
if [ -f "${HERE}/mcp_file_server.py" ]; then
    PYTHON_SCRIPT="${HERE}/mcp_file_server.py"
elif [ -f "${HERE}/../share/mini_tool/mcp_file_server.py" ]; then
    PYTHON_SCRIPT="${HERE}/../share/mini_tool/mcp_file_server.py"
else
    echo "错误: 找不到mcp_file_server.py" >&2
    exit 1
fi

# 检查Python是否可用
if ! command -v python3 &> /dev/null; then
    echo "错误: 系统未安装Python3" >&2
    exit 1
fi

# 运行Python服务器
exec python3 "$PYTHON_SCRIPT" "$@"
