#ifndef REPLACEALGORITHMDIALOG_H
#define REPLACEALGORITHMDIALOG_H

#include <wx/wx.h>
#include <wx/filepicker.h>
#include <wx/dirctrl.h>

class ReplaceAlgorithmDialog : public wxDialog
{
public:
    ReplaceAlgorithmDialog(wxWindow* parent);

private:
    // Event handlers
    void OnOK(wxCommandEvent& event);
    void OnCancel(wxCommandEvent& event);
    void OnFileChanged(wxFileDirPickerEvent& event);
    void OnDirChanged(wxFileDirPickerEvent& event);
    void OnOptionChanged(wxCommandEvent& event);

    // Helper functions
    bool ValidateFiles();
    bool CopyFilesToDestination();
    bool StartCompilation();
    void CleanupReplacedFiles();
    bool CopyDirectory(const wxString& source, const wxString& destination);
    void UpdateFilePickersEnable();

    // UI components
    wxFilePickerCtrl* m_mgsFlowPicker;
    wxFilePickerCtrl* m_cImgDataBufferPicker;
    wxFilePickerCtrl* m_classificationProcessPicker;
    wxFilePickerCtrl* m_generalProcessPicker;
    wxFilePickerCtrl* m_generalProcessHPicker;
    wxFilePickerCtrl* m_processPicker;
    wxDirPickerCtrl* m_algorithmDataPicker;

    wxButton* m_okButton;
    wxButton* m_cancelButton;

    // 新增：选项CheckBox
    wxCheckBox* m_debugDiskCheckBox;
    wxCheckBox* m_algorithmRecompileCheckBox;

    // File paths
    wxString m_mgsFlowPath;
    wxString m_cImgDataBufferPath;
    wxString m_classificationProcessPath;
    wxString m_generalProcessPath;
    wxString m_generalProcessHPath;
    wxString m_processPath;
    wxString m_algorithmDataPath;

    // Constants
    static const wxString DESTINATION_PATH;
    static const wxString ALGORITHM_DATA_DESTINATION;

    // Button IDs
    enum
    {
        ID_OK_BTN = 2001,
        ID_CANCEL_BTN = 2002,
        ID_MGS_FLOW_PICKER = 2003,
        ID_CIMG_PICKER = 2004,
        ID_CLASSIFICATION_PICKER = 2005,
        ID_GENERAL_PICKER = 2006,
        ID_GENERAL_H_PICKER = 2007,
        ID_PROCESS_PICKER = 2008,
        ID_ALGORITHM_DATA_PICKER = 2009,
        ID_DEBUG_DISK_CHECKBOX = 2010,
        ID_ALGORITHM_RECOMPILE_CHECKBOX = 2011
    };

    wxDECLARE_EVENT_TABLE();
};

#endif // REPLACEALGORITHMDIALOG_H
