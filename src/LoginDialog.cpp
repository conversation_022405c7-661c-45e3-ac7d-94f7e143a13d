#include "LoginDialog.h"
#include <wx/sizer.h>
#include <wx/stattext.h>
#include <wx/msgdlg.h>

// Event table
wxBEGIN_EVENT_TABLE(LoginDialog, wxDialog)
    EVT_BUTTON(ID_OK_BTN, LoginDialog::OnOK)
    EVT_BUTTON(ID_CANCEL_BTN, LoginDialog::OnCancel)
    EVT_TEXT_ENTER(ID_USERNAME_CTRL, LoginDialog::OnTextEnter)
    EVT_TEXT_ENTER(ID_PASSWORD_CTRL, LoginDialog::OnTextEnter)
wxEND_EVENT_TABLE()

LoginDialog::LoginDialog(wxWindow* parent)
    : wxDialog(parent, wxID_ANY, wxT("用户登录"), wxDefaultPosition, wxSize(400, 350),
               wxDEFAULT_DIALOG_STYLE & ~wxRESIZE_BORDER)
{
    CreateControls();
    CenterOnParent();
    
    // Set focus to username field
    m_usernameCtrl->SetFocus();
}

void LoginDialog::CreateControls()
{
    // Main sizer
    wxBoxSizer* mainSizer = new wxBoxSizer(wxVERTICAL);
    
    // Title
    wxStaticText* titleLabel = new wxStaticText(this, wxID_ANY, wxT("登录"));
    wxFont titleFont = titleLabel->GetFont();
    titleFont.SetPointSize(16);
    titleFont.SetWeight(wxFONTWEIGHT_BOLD);
    titleLabel->SetFont(titleFont);
    mainSizer->Add(titleLabel, 0, wxALL | wxALIGN_CENTER, 20);
    
    // Login form
    wxStaticBoxSizer* formBox = new wxStaticBoxSizer(wxVERTICAL, this, wxT("登录信息"));
    
    // Username
    wxBoxSizer* userSizer = new wxBoxSizer(wxHORIZONTAL);
    userSizer->Add(new wxStaticText(this, wxID_ANY, wxT("用户名:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 10);
    m_usernameCtrl = new wxTextCtrl(this, ID_USERNAME_CTRL, wxEmptyString, wxDefaultPosition, wxSize(200, 30),
                                   wxTE_PROCESS_ENTER);
    userSizer->Add(m_usernameCtrl, 1, wxEXPAND);
    formBox->Add(userSizer, 0, wxALL | wxEXPAND, 10);
    
    // Password
    wxBoxSizer* passSizer = new wxBoxSizer(wxHORIZONTAL);
    passSizer->Add(new wxStaticText(this, wxID_ANY, wxT("密码:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 10);
    m_passwordCtrl = new wxTextCtrl(this, ID_PASSWORD_CTRL, wxEmptyString, wxDefaultPosition, wxSize(200, 30),
                                   wxTE_PASSWORD | wxTE_PROCESS_ENTER);
    passSizer->Add(m_passwordCtrl, 1, wxEXPAND);
    formBox->Add(passSizer, 0, wxALL | wxEXPAND, 10);
    
    // Error label (initially hidden)
    m_errorLabel = new wxStaticText(this, wxID_ANY, wxEmptyString);
    m_errorLabel->SetForegroundColour(*wxRED);
    m_errorLabel->Show(false);  // Initially hidden
    formBox->Add(m_errorLabel, 0, wxALL | wxEXPAND, 10);
    
    mainSizer->Add(formBox, 1, wxALL | wxEXPAND, 20);
    
    // Buttons
    wxBoxSizer* buttonSizer = new wxBoxSizer(wxHORIZONTAL);
    buttonSizer->AddStretchSpacer(1);
    
    m_okButton = new wxButton(this, ID_OK_BTN, wxT("登录"), wxDefaultPosition, wxSize(80, 35));
    m_okButton->SetDefault();
    buttonSizer->Add(m_okButton, 0, wxRIGHT, 10);
    
    m_cancelButton = new wxButton(this, ID_CANCEL_BTN, wxT("取消"), wxDefaultPosition, wxSize(80, 35));
    buttonSizer->Add(m_cancelButton, 0);
    
    buttonSizer->AddStretchSpacer(1);
    mainSizer->Add(buttonSizer, 0, wxALL | wxEXPAND, 20);
    
    SetSizer(mainSizer);
}

void LoginDialog::OnOK(wxCommandEvent& WXUNUSED(event))
{
    wxString username = m_usernameCtrl->GetValue().Trim().Trim(false);
    wxString password = m_passwordCtrl->GetValue();
    
    // Clear previous error
    ClearError();
    
    // Basic validation
    if (username.IsEmpty()) {
        ShowError(wxT("请输入用户名"));
        m_usernameCtrl->SetFocus();
        return;
    }
    
    if (password.IsEmpty()) {
        ShowError(wxT("请输入密码"));
        m_passwordCtrl->SetFocus();
        return;
    }
    
    // Validate credentials
    if (!ValidateCredentials(username, password)) {
        ShowError(wxT("用户名或密码错误"));
        m_passwordCtrl->Clear();
        m_passwordCtrl->SetFocus();
        return;
    }
    
    // Store credentials and close dialog
    m_username = username;
    m_password = password;
    EndModal(wxID_OK);
}

void LoginDialog::OnCancel(wxCommandEvent& WXUNUSED(event))
{
    EndModal(wxID_CANCEL);
}

void LoginDialog::OnTextEnter(wxCommandEvent& event)
{
    if (event.GetId() == ID_USERNAME_CTRL) {
        // Move focus to password field
        m_passwordCtrl->SetFocus();
    } else if (event.GetId() == ID_PASSWORD_CTRL) {
        // Trigger login
        wxCommandEvent loginEvent(wxEVT_COMMAND_BUTTON_CLICKED, ID_OK_BTN);
        ProcessEvent(loginEvent);
    }
}

bool LoginDialog::ValidateCredentials(const wxString& username, const wxString& password)
{
    // Simple hardcoded credentials for now
    // In a real application, this would check against a database or config file
    return (username == wxT("admin") && password == wxT("admin")) ||
           (username == wxT("nuctech") && password == wxT("nuctech123")) ||
           (username == wxT("user") && password == wxT("password"));
}

void LoginDialog::ShowError(const wxString& message)
{
    m_errorLabel->SetLabel(message);
    m_errorLabel->Show(true);
    m_errorLabel->Wrap(450);  // Set wrap width
    GetSizer()->Layout();     // Layout the main sizer
    Fit();                    // Resize dialog to fit content
    Refresh();
}

void LoginDialog::ClearError()
{
    m_errorLabel->SetLabel(wxEmptyString);
    m_errorLabel->Show(false);
    GetSizer()->Layout();     // Layout the main sizer
    Fit();                    // Resize dialog to fit content
    Refresh();
}
